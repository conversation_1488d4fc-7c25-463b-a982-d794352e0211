#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证修改后的代码
"""

import numpy as np

def test_complete_workflow():
    """测试完整的工作流程"""
    print("=== 完整工作流程测试 ===")
    
    try:
        from all import MFTNModel, DEFAULT_CONFIG
        
        # 1. 验证配置
        print("1. 验证配置...")
        expected_weights = ['co_weight', 'digit_transition_weight', 'sum_transition_weight']
        for weight in expected_weights:
            if weight not in DEFAULT_CONFIG:
                print(f"✗ 缺少权重参数: {weight}")
                return False
            print(f"  ✓ {weight}: {DEFAULT_CONFIG[weight]}")
        
        weight_sum = sum(DEFAULT_CONFIG[w] for w in expected_weights)
        if abs(weight_sum - 1.0) > 0.01:
            print(f"✗ 权重之和不正确: {weight_sum}")
            return False
        print(f"  ✓ 权重之和: {weight_sum}")
        
        # 2. 创建和训练模型
        print("\n2. 创建和训练模型...")
        model = MFTNModel()
        
        # 创建更真实的测试数据
        np.random.seed(42)
        test_data = np.random.randint(0, 10, size=(200, 5))
        print(f"  测试数据形状: {test_data.shape}")
        
        model.fit(test_data)
        print("  ✓ 模型训练成功")
        
        # 3. 验证转移矩阵
        print("\n3. 验证转移矩阵...")
        if model.digit_transition_probs is None:
            print("  ✗ 数字转移矩阵未创建")
            return False
        print(f"  ✓ 数字转移矩阵形状: {model.digit_transition_probs.shape}")
        
        if model.sum_transition_probs is None:
            print("  ✗ 数字和转移矩阵未创建")
            return False
        print(f"  ✓ 数字和转移矩阵形状: {model.sum_transition_probs.shape}")
        
        # 4. 测试预测功能
        print("\n4. 测试预测功能...")
        predictions = model.predict_next(is_predict=True)
        
        if not isinstance(predictions, dict) or len(predictions) != 5:
            print(f"  ✗ 预测结果格式错误: {predictions}")
            return False
        
        for pos in range(5):
            if pos not in predictions:
                print(f"  ✗ 缺少位置 {pos} 的预测")
                return False
            pred = predictions[pos]
            if not isinstance(pred, list) or len(pred) == 0:
                print(f"  ✗ 位置 {pos} 预测格式错误: {pred}")
                return False
            print(f"  ✓ 位置 {pos}: {len(pred)} 个预测数字")
        
        # 5. 测试数字和转移预测
        print("\n5. 测试数字和转移预测...")
        for pos in range(5):
            sum_pred = model._sum_transition_predict(pos)
            if sum_pred is None or len(sum_pred) != 10:
                print(f"  ✗ 位置 {pos} 数字和转移预测失败")
                return False
            if abs(np.sum(sum_pred) - 1.0) > 0.01:
                print(f"  ✗ 位置 {pos} 概率和不为1: {np.sum(sum_pred)}")
                return False
        print("  ✓ 所有位置的数字和转移预测正常")
        
        # 6. 测试权重融合
        print("\n6. 测试权重融合...")
        for pos in range(5):
            co_pred = model._co_predict(pos)
            digit_pred = model._digit_transition_predict(pos)
            sum_pred = model._sum_transition_predict(pos)
            
            # 手动计算融合权重
            base_weight_sum = (model.params['co_weight'] + 
                              model.params['digit_transition_weight'] + 
                              model.params['sum_transition_weight'])
            
            manual_weights = (model.params['co_weight'] / base_weight_sum) * co_pred + \
                           (model.params['digit_transition_weight'] / base_weight_sum) * digit_pred + \
                           (model.params['sum_transition_weight'] / base_weight_sum) * sum_pred
            
            if abs(np.sum(manual_weights) - 1.0) > 0.01:
                print(f"  ✗ 位置 {pos} 融合权重和不为1: {np.sum(manual_weights)}")
                return False
        print("  ✓ 权重融合计算正确")
        
        print("\n🎉 所有测试通过！修改成功完成！")
        return True
        
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_removed_methods():
    """验证已删除的方法确实不存在"""
    print("\n=== 验证已删除方法 ===")
    
    try:
        from all import MFTNModel
        model = MFTNModel()
        
        removed_methods = ['_short_term_predict', '_mid_term_predict', '_long_term_predict']
        for method in removed_methods:
            if hasattr(model, method):
                print(f"✗ 方法 {method} 仍然存在，应该已被删除")
                return False
            else:
                print(f"✓ 方法 {method} 已成功删除")
        
        return True
        
    except Exception as e:
        print(f"✗ 验证删除方法时发生错误: {e}")
        return False

def main():
    """主测试函数"""
    print("开始最终验证...")
    print("="*60)
    
    success = True
    
    # 测试完整工作流程
    if not test_complete_workflow():
        success = False
    
    # 验证已删除的方法
    if not test_removed_methods():
        success = False
    
    print("\n" + "="*60)
    if success:
        print("🎉 最终验证通过！代码修改完全成功！")
        print("\n修改总结:")
        print("✓ 删除了短期、中期、长期权重和对应的预测方法")
        print("✓ 新增了数字和转移权重和对应的预测方法")
        print("✓ 更新了所有相关的参数处理逻辑")
        print("✓ 保持了原有接口的兼容性")
        print("✓ 所有功能正常工作")
    else:
        print("❌ 最终验证失败，需要进一步检查")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
