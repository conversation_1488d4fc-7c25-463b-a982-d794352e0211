#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的数字排除权重功能
"""

import numpy as np

def test_improved_exclusion():
    """测试改进后的数字排除功能"""
    print("=== 测试改进后的数字排除功能 ===")
    
    try:
        from all import MFTNModel, DEFAULT_CONFIG
        
        # 验证配置
        print("1. 验证配置...")
        weights = ['co_weight', 'digit_transition_weight', 'digit_exclusion_weight']
        weight_sum = sum(DEFAULT_CONFIG.get(w, 0) for w in weights)
        print(f"  权重配置: {[f'{w}={DEFAULT_CONFIG[w]}' for w in weights]}")
        print(f"  权重之和: {weight_sum}")
        
        # 创建有明显规律的测试数据
        print("\n2. 创建有规律的测试数据...")
        test_data = []
        
        # 创建200期数据，建立明确的排除规律
        for i in range(200):
            period = np.random.randint(0, 10, size=5)
            
            # 建立规律：如果本期位置0是数字7，那么下期位置1和位置3很少出现7
            if i > 0:
                prev_period = test_data[i-1]
                if prev_period[0] == 7:  # 如果上期位置0是7
                    # 下期位置1和3避免出现7（90%概率避免）
                    if np.random.random() < 0.9:
                        while period[1] == 7:
                            period[1] = np.random.randint(0, 7)
                    if np.random.random() < 0.9:
                        while period[3] == 7:
                            period[3] = np.random.randint(0, 7)
                
                # 建立另一个规律：如果本期位置2是数字3，那么下期位置0和位置4很少出现3
                if prev_period[2] == 3:
                    if np.random.random() < 0.85:
                        while period[0] == 3:
                            period[0] = np.random.randint(4, 10)
                    if np.random.random() < 0.85:
                        while period[4] == 3:
                            period[4] = np.random.randint(4, 10)
            
            test_data.append(period)
        
        test_data = np.array(test_data)
        print(f"  测试数据形状: {test_data.shape}")
        
        # 训练模型
        print("\n3. 训练模型...")
        model = MFTNModel()
        model.fit(test_data)
        print("  ✓ 模型训练成功")
        
        # 验证排除矩阵
        print("\n4. 验证排除矩阵...")
        if model.digit_exclusion_matrix is None:
            print("  ✗ 数字排除矩阵未创建")
            return False
        
        print(f"  ✓ 数字排除矩阵形状: {model.digit_exclusion_matrix.shape}")
        
        # 检查特定的排除规律
        print("\n5. 检查排除规律...")
        
        # 检查位置0的数字7对其他位置的影响
        exclusion_weights_7 = model.digit_exclusion_matrix[0, 7, :]
        print(f"  位置0数字7对各位置的权重: {exclusion_weights_7}")
        
        excluded_positions_7 = np.where(exclusion_weights_7 < 1.0)[0]
        if len(excluded_positions_7) > 0:
            print(f"  ✓ 数字7被排除的位置: {excluded_positions_7}")
        else:
            print("  - 数字7没有明显的排除位置")
        
        # 检查位置2的数字3对其他位置的影响
        exclusion_weights_3 = model.digit_exclusion_matrix[2, 3, :]
        print(f"  位置2数字3对各位置的权重: {exclusion_weights_3}")
        
        excluded_positions_3 = np.where(exclusion_weights_3 < 1.0)[0]
        if len(excluded_positions_3) > 0:
            print(f"  ✓ 数字3被排除的位置: {excluded_positions_3}")
        else:
            print("  - 数字3没有明显的排除位置")
        
        # 测试预测功能
        print("\n6. 测试预测功能...")
        
        # 设置特定的测试场景
        test_scenarios = [
            [7, 1, 2, 4, 6],  # 位置0是7，应该影响位置1和3
            [2, 5, 3, 8, 9],  # 位置2是3，应该影响位置0和4
        ]
        
        for scenario_idx, test_period in enumerate(test_scenarios):
            print(f"\n  场景 {scenario_idx + 1}: 上期数据 {test_period}")
            
            # 设置测试数据
            model.history = np.vstack([model.history, np.array(test_period).reshape(1, -1)])
            
            # 进行预测
            predictions = model.predict_next(is_predict=True)
            
            for pos in range(5):
                pred = predictions[pos]
                print(f"    位置 {pos}: {len(pred)} 个预测数字")
                
                # 检查排除效果
                if scenario_idx == 0 and pos in [1, 3]:  # 第一个场景，位置1和3应该排除数字7
                    if 7 not in pred:
                        print(f"      ✓ 数字7被成功排除")
                    else:
                        print(f"      - 数字7未被排除")
                
                if scenario_idx == 1 and pos in [0, 4]:  # 第二个场景，位置0和4应该排除数字3
                    if 3 not in pred:
                        print(f"      ✓ 数字3被成功排除")
                    else:
                        print(f"      - 数字3未被排除")
        
        print("\n🎉 改进后的数字排除功能测试完成！")
        return True
        
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试改进后的数字排除权重功能...")
    print("="*60)
    
    success = test_improved_exclusion()
    
    print("\n" + "="*60)
    if success:
        print("🎉 改进后的数字排除权重功能测试通过！")
        print("\n功能特点:")
        print("✓ 统计本期各位置数字在下期各位置的出现频率")
        print("✓ 智能识别明显偏低的出现概率")
        print("✓ 只对明显不太可能的位置进行排除")
        print("✓ 对数据不足的情况使用轻微的随机排除")
        print("✓ 在预测时应用排除权重，提高预测准确性")
    else:
        print("❌ 改进后的数字排除权重功能需要进一步调整")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
