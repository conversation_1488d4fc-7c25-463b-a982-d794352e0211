# 数字排除权重详细说明

## 概述
数字排除权重是一种基于历史数据统计分析的预测优化机制，通过识别某些数字在特定位置出现的"反规律"，来降低这些不太可能出现的数字的选择概率，从而提高预测的精准度。

## 核心思想
**基本假设**：如果本期某个位置出现了数字X，那么下期某些位置出现数字X的概率会明显降低。通过统计历史数据，我们可以找出这些"排除规律"并应用到预测中。

## 工作原理详解

### 第一步：数据统计分析
```python
def _build_digit_exclusion_matrix(self):
    # 初始化统计矩阵：[当前位置][数字][下期位置] = 出现次数
    exclusion_counts = np.zeros((5, 10, 5))
    
    # 遍历历史数据，统计转移频率
    for i in range(历史期数 - 1):
        本期数据 = self.history[i]      # 例如：[3, 7, 1, 9, 2]
        下期数据 = self.history[i + 1]  # 例如：[5, 2, 8, 1, 6]
        
        for 当前位置 in range(5):
            当前数字 = 本期数据[当前位置]
            for 下期位置 in range(5):
                下期数字 = 下期数据[下期位置]
                if 当前数字 == 下期数字:
                    exclusion_counts[当前位置, 当前数字, 下期位置] += 1
```

**实际例子**：
```
本期：位置0=3, 位置1=7, 位置2=1, 位置3=9, 位置4=2
下期：位置0=5, 位置1=2, 位置2=8, 位置3=1, 位置4=6

统计结果：
- 位置0的数字3 → 下期位置0出现3: 0次
- 位置0的数字3 → 下期位置1出现3: 0次  
- 位置0的数字3 → 下期位置2出现3: 0次
- 位置0的数字3 → 下期位置3出现3: 0次
- 位置0的数字3 → 下期位置4出现3: 0次
- 位置2的数字1 → 下期位置3出现1: 1次 ✓
```

### 第二步：概率计算与排除识别
```python
for 当前位置 in range(5):
    for 数字 in range(10):
        # 获取该数字在各位置的出现频率
        frequencies = exclusion_counts[当前位置, 数字]  # [f0, f1, f2, f3, f4]
        total_count = sum(frequencies)
        
        if total_count >= 2:  # 数据充足才进行分析
            # 计算概率分布
            probabilities = frequencies / total_count
            
            # 找出概率最小的两个位置
            sorted_indices = np.argsort(probabilities)
            least_likely_positions = sorted_indices[:2]  # 最不可能的2个位置
```

**实际例子**：
```
假设位置0的数字7在历史数据中的统计：
- 下期位置0出现7: 15次
- 下期位置1出现7: 3次  ← 最少
- 下期位置2出现7: 12次
- 下期位置3出现7: 5次  ← 第二少
- 下期位置4出现7: 18次
总计：53次

概率分布：
- 位置0: 15/53 = 0.283 (28.3%)
- 位置1: 3/53 = 0.057 (5.7%)   ← 最低
- 位置2: 12/53 = 0.226 (22.6%)
- 位置3: 5/53 = 0.094 (9.4%)   ← 第二低
- 位置4: 18/53 = 0.340 (34.0%)

平均概率 = 1/5 = 0.2 (20%)
```

### 第三步：智能排除策略
```python
avg_prob = 1.0 / 5  # 平均概率 = 20%
min_prob = probabilities[least_likely_positions[0]]  # 最小概率

if min_prob < avg_prob * 0.7:  # 如果最小概率 < 14%
    # 对最不可能的两个位置设置排除权重
    for pos in least_likely_positions:
        if probabilities[pos] < avg_prob * 0.8:  # 如果概率 < 16%
            self.digit_exclusion_matrix[当前位置, 数字, pos] = 0.3  # 减小到30%
```

**排除条件**：
- **显著性检验**：最小概率必须小于平均概率的70%（即14%）
- **双重确认**：只对概率小于平均概率80%（即16%）的位置进行排除
- **排除强度**：将权重减小到30%（而不是完全排除）

**继续上面的例子**：
```
位置0数字7的排除分析：
- 位置1概率：5.7% < 14% ✓ 符合排除条件
- 位置3概率：9.4% < 16% ✓ 符合排除条件

结果：
- digit_exclusion_matrix[0, 7, 1] = 0.3  # 位置1的数字7权重减小到30%
- digit_exclusion_matrix[0, 7, 3] = 0.3  # 位置3的数字7权重减小到30%
```

### 第四步：预测时应用排除权重
```python
def _digit_exclusion_predict(self, position):
    # 获取上一期各位置的数字
    last_period = self.history[-1]  # 例如：[7, 1, 2, 4, 6]
    
    # 初始化预测概率（均匀分布）
    prediction_probs = np.ones(10) / 10  # 每个数字10%概率
    
    # 根据上一期各位置的数字，调整当前位置各数字的权重
    for source_pos in range(5):
        source_digit = last_period[source_pos]
        # 获取排除权重
        exclusion_weight = self.digit_exclusion_matrix[source_pos, source_digit, position]
        # 调整概率
        prediction_probs[source_digit] *= exclusion_weight
```

**实际应用例子**：
```
假设上一期数据：[7, 1, 2, 4, 6]
预测位置1的数字：

初始概率：每个数字都是10%

应用排除权重：
- 数字7（来自位置0）：
  exclusion_weight = digit_exclusion_matrix[0, 7, 1] = 0.3
  prediction_probs[7] = 10% × 0.3 = 3%  ← 被排除

- 数字1（来自位置1）：
  exclusion_weight = digit_exclusion_matrix[1, 1, 1] = 1.0（假设）
  prediction_probs[1] = 10% × 1.0 = 10%  ← 不变

- 其他数字类似处理...

最终概率分布（归一化后）：
- 数字0: 11.5%
- 数字1: 11.5%
- 数字2: 11.5%
- 数字3: 11.5%
- 数字4: 11.5%
- 数字5: 11.5%
- 数字6: 11.5%
- 数字7: 3.4%   ← 被排除，概率大幅降低
- 数字8: 11.5%
- 数字9: 11.5%
```

## 排除机制的特点

### 1. 智能化排除
- **不是盲目排除**：基于统计显著性，只排除明显不太可能的情况
- **不是完全排除**：减小权重到30%，而不是完全排除（0%）
- **双重验证**：需要同时满足两个条件才进行排除

### 2. 自适应调整
- **数据充足性检查**：至少需要2次出现才进行排除计算
- **显著性检验**：只有当概率明显偏低时才排除
- **保守策略**：对数据不足的情况使用轻微排除（70%权重）

### 3. 多维度影响
- **位置相关**：不同位置的同一数字有不同的排除规律
- **数字相关**：不同数字有不同的排除模式
- **历史相关**：基于完整历史数据的统计分析

## 实际效果

### 排除示例
通过历史数据分析发现的典型排除规律：
```
位置0的数字7 → 排除位置1和位置3
位置2的数字3 → 排除位置0和位置4
位置1的数字5 → 排除位置2和位置3
...
```

### 预测改善
- **提高精准度**：排除不太可能的数字，集中概率到更可能的数字上
- **保持覆盖面**：不完全排除，保留一定概率避免遗漏
- **统计可靠**：基于大量历史数据的统计规律，而非随机猜测

## 参数调节

### digit_exclusion_weight (0.0-1.0)
- **0.0**：完全不使用排除权重
- **0.3**：默认值，平衡排除效果和其他预测方法
- **1.0**：完全依赖排除权重（不推荐）

### 与其他权重的配合
```python
最终预测 = co_weight × 协同预测 + 
          digit_transition_weight × 数字转移预测 + 
          digit_exclusion_weight × 数字排除预测
```

## 总结
数字排除权重通过深度分析历史数据中的"反规律"，智能识别并排除不太可能出现的数字位置组合，从而提高预测的精准度。这是一种基于统计学原理的科学方法，既保持了预测的覆盖面，又提高了命中的概率。
