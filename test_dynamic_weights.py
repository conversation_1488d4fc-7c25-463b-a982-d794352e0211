#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态权重功能测试脚本
"""

import sys
import os
import numpy as np
import time

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入MFTNModel类
from all_copy_2 import MFTNModel

def test_dynamic_weights():
    """测试动态权重功能"""
    print("=== 动态权重功能测试 ===\n")
    
    # 创建测试数据
    print("1. 创建测试数据...")
    np.random.seed(42)  # 设置随机种子确保结果可复现
    
    # 生成模拟的历史数据
    n_periods = 100
    n_positions = 5
    test_data = np.random.randint(0, 10, size=(n_periods, n_positions))
    
    print(f"   生成 {n_periods} 期历史数据，每期 {n_positions} 个位置")
    print(f"   数据形状: {test_data.shape}")
    
    # 创建模型
    print("\n2. 创建MFTN模型...")
    model = MFTNModel()
    
    # 训练模型
    print("3. 训练模型...")
    model.fit(test_data)
    print("   模型训练完成")
    
    # 测试固定权重模式
    print("\n4. 测试固定权重模式...")
    print("   当前预测历史数量:", len(model.predictions_history))
    print("   是否使用动态权重:", model._should_use_dynamic_weights())
    
    # 进行多次预测以积累历史数据
    print("\n5. 进行多次预测以积累历史数据...")
    for i in range(15):  # 进行15次预测
        # 模拟实际结果
        actual_result = np.random.randint(0, 10, size=n_positions)
        
        # 进行预测
        predictions = model.predict_next(is_predict=True)
        
        # 更新实际结果
        for pos in range(n_positions):
            model.current_actual = actual_result
            # 模拟预测历史记录
            model.predictions_history.append({
                'predicted': predictions[pos],
                'actual': [actual_result[pos]],
                'timestamp': time.time()
            })
        
        print(f"   第{i+1}次预测完成，历史记录数量: {len(model.predictions_history)}")
        
        # 检查是否激活动态权重
        if model._should_use_dynamic_weights():
            print(f"   ✅ 动态权重已激活！")
            break
    
    # 测试动态权重功能
    print("\n6. 测试动态权重功能...")
    
    # 获取动态权重统计
    dynamic_stats = model.get_dynamic_weight_stats()
    if dynamic_stats:
        print("   动态权重统计信息:")
        print(f"   总预测次数: {dynamic_stats['total_predictions']}")
        print(f"   是否使用动态权重: {dynamic_stats['is_using_dynamic']}")
        
        if dynamic_stats['current_weights']:
            print("   当前动态权重:")
            for name, weight in dynamic_stats['current_weights'].items():
                print(f"     {name}_weight: {weight:.4f}")
        
        print("   历史平均权重:")
        for name, weight in dynamic_stats['average_weights'].items():
            print(f"     avg_{name}: {weight:.4f}")
    
    # 测试权重计算
    print("\n7. 测试权重计算方法...")
    
    # 测试短期权重计算
    short_weight = model._calculate_short_term_weight()
    print(f"   短期权重: {short_weight:.4f}")
    
    # 测试中期权重计算
    mid_weight = model._calculate_mid_term_weight()
    print(f"   中期权重: {mid_weight:.4f}")
    
    # 测试长期权重计算
    long_weight = model._calculate_long_term_weight()
    print(f"   长期权重: {long_weight:.4f}")
    
    # 测试协同权重计算
    co_weight = model._calculate_co_prediction_weight()
    print(f"   协同权重: {co_weight:.4f}")
    
    # 测试动态权重计算
    dynamic_weights = model._calculate_dynamic_weights()
    print("\n   动态权重计算结果:")
    total_weight = 0
    for name, weight in dynamic_weights.items():
        print(f"     {name}_weight: {weight:.4f}")
        total_weight += weight
    print(f"   权重总和: {total_weight:.4f} (应该接近1.0)")
    
    # 验证权重归一化
    if abs(total_weight - 1.0) < 0.01:
        print("   ✅ 权重归一化正确")
    else:
        print("   ❌ 权重归一化有问题")
    
    print("\n=== 测试完成 ===")
    print("动态权重功能测试成功！")
    
    return model

def test_weight_transition():
    """测试权重切换功能"""
    print("\n=== 权重切换测试 ===\n")
    
    # 创建模型
    model = MFTNModel()
    
    # 生成测试数据
    test_data = np.random.randint(0, 10, size=(50, 5))
    model.fit(test_data)
    
    print("1. 初始状态测试...")
    print(f"   预测历史数量: {len(model.predictions_history)}")
    print(f"   是否使用动态权重: {model._should_use_dynamic_weights()}")
    
    # 模拟预测过程
    print("\n2. 模拟预测过程...")
    for i in range(12):  # 进行12次预测
        actual_result = np.random.randint(0, 10, size=5)
        predictions = model.predict_next(is_predict=True)
        
        # 更新历史记录
        for pos in range(5):
            model.current_actual = actual_result
            model.predictions_history.append({
                'predicted': predictions[pos],
                'actual': [actual_result[pos]],
                'timestamp': time.time()
            })
        
        print(f"   第{i+1}次预测 - 历史记录: {len(model.predictions_history)} - 动态权重: {model._should_use_dynamic_weights()}")
        
        # 在第10次预测后检查切换
        if i == 9:
            print(f"   🎯 第10次预测后，动态权重状态: {model._should_use_dynamic_weights()}")
    
    print("\n3. 权重切换验证...")
    if model._should_use_dynamic_weights():
        print("   ✅ 动态权重成功激活")
        
        # 获取动态权重
        dynamic_weights = model._calculate_dynamic_weights()
        print("   动态权重值:")
        for name, weight in dynamic_weights.items():
            print(f"     {name}: {weight:.4f}")
    else:
        print("   ❌ 动态权重未激活")
    
    print("\n=== 权重切换测试完成 ===")

if __name__ == "__main__":
    try:
        # 运行基本测试
        model = test_dynamic_weights()
        
        # 运行权重切换测试
        test_weight_transition()
        
        print("\n🎉 所有测试通过！动态权重功能正常工作。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc() 