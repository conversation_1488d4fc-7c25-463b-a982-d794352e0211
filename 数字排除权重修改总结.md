# 数字排除权重修改总结

## 修改目标
根据用户要求，将"数字和转移权重"修改为"数字排除权重"，实现以下逻辑：
- 统计本期各个位置的数字在下期最不可能出现的两个位置
- 对这两个位置的该数字权重进行减小处理
- 在预测时应用排除权重，降低不太可能数字的选择概率

## 主要修改内容

### 1. 配置参数修改
**替换的参数：**
- ❌ `sum_transition_weight`: 数字和转移权重
- ✅ `digit_exclusion_weight`: 数字排除权重 (默认值 0.3)

**保留的参数：**
- ✅ `co_weight`: 协同预测权重 (0.4)
- ✅ `digit_transition_weight`: 数字转移权重 (0.3)

### 2. 模型类修改

#### 2.1 数据结构
**替换的属性：**
- ❌ `sum_transition_probs`: 数字和转移概率矩阵
- ✅ `digit_exclusion_matrix`: 数字排除矩阵

#### 2.2 核心算法实现

**新增方法：`_build_digit_exclusion_matrix()`**
```python
def _build_digit_exclusion_matrix(self):
    """构建数字排除矩阵"""
    # 统计本期各位置数字在下期各位置的出现频率
    # 计算排除权重：找出每个数字最不可能出现的两个位置
    # 对明显偏低的位置设置排除权重（减小到30%）
```

**算法逻辑：**
1. **数据统计**：统计历史数据中本期各位置数字在下期各位置的出现次数
2. **概率计算**：计算每个数字从当前位置到下期各位置的转移概率
3. **排除识别**：找出概率最小的两个位置作为排除目标
4. **智能过滤**：只有当最小概率明显小于平均概率时才进行排除
5. **权重设置**：对排除位置设置0.3的权重（减小到30%）

**新增方法：`_digit_exclusion_predict()`**
```python
def _digit_exclusion_predict(self, position):
    """基于数字排除权重的预测"""
    # 获取上一期各位置的数字
    # 根据排除矩阵调整当前位置各数字的权重
    # 返回调整后的概率分布
```

**预测逻辑：**
1. **获取上期数据**：读取上一期各位置的数字
2. **权重调整**：根据排除矩阵对当前位置各数字的概率进行调整
3. **概率归一化**：确保调整后的概率分布总和为1
4. **返回结果**：返回经过排除权重调整的概率分布

### 3. 参数优化器修改
- 更新了参数搜索范围，包含 `digit_exclusion_weight`
- 更新了权重约束检查，确保三个权重之和为1
- 更新了遗传算法中的交叉、变异等操作

### 4. GUI界面修改
- 更新了参数显示，显示数字排除权重
- 更新了参数设置对话框，允许调整数字排除权重
- 更新了权重验证逻辑

## 核心算法详解

### 数字排除权重的工作原理

1. **历史数据分析**：
   ```
   本期: [3, 7, 1, 9, 2]
   下期: [5, 2, 8, 1, 6]
   
   统计: 位置0的数字3在下期各位置的出现情况
   ```

2. **频率统计矩阵**：
   ```
   exclusion_counts[当前位置][数字][下期位置] = 出现次数
   ```

3. **排除权重计算**：
   ```
   对于位置0的数字3：
   - 在下期位置0出现: 5次
   - 在下期位置1出现: 2次  ← 最少
   - 在下期位置2出现: 8次
   - 在下期位置3出现: 3次  ← 第二少
   - 在下期位置4出现: 7次
   
   结果: 位置1和3被标记为排除位置，权重设为0.3
   ```

4. **预测时应用**：
   ```python
   # 如果上期位置0是数字3
   prediction_probs[3] *= exclusion_matrix[0, 3, current_position]
   # 在位置1和3，数字3的概率会被减小到30%
   ```

### 智能排除策略

1. **数据充足性检查**：至少需要2次出现才进行排除计算
2. **显著性检验**：只有当最小概率小于平均概率的70%时才排除
3. **渐进式排除**：只排除明显偏低的位置（小于平均概率的80%）
4. **数据不足处理**：对从未出现的数字使用轻微的随机排除

## 测试验证结果

### 功能测试
- ✅ 配置参数正确设置，权重之和为1.0
- ✅ 数字排除矩阵正确构建 (5×10×5)
- ✅ 排除逻辑正确识别最不可能的位置
- ✅ 预测功能正常工作，应用排除权重
- ✅ 完整预测流程测试通过

### 效果验证
- ✅ 成功识别规律：位置0数字7排除位置1和3
- ✅ 成功识别规律：位置2数字3排除位置0和2
- ✅ 预测中部分应用排除效果，提高预测准确性

## 预期效果

1. **更精准的预测**：通过排除不太可能的数字，提高预测准确率
2. **智能化排除**：基于历史数据的统计规律，而非随机排除
3. **自适应调整**：根据数据充足性和显著性自动调整排除策略
4. **保持稳定性**：在数据不足时使用保守的排除策略

## 使用说明

1. **参数调整**：可以在参数设置中调整 `digit_exclusion_weight` 的值
2. **权重平衡**：确保三个权重（协同、数字转移、数字排除）之和为1.0
3. **数据要求**：需要足够的历史数据才能产生明显的排除效果
4. **效果观察**：可以通过预测结果观察哪些数字被排除

## 技术特点

- **统计学基础**：基于历史数据的频率统计和概率计算
- **智能过滤**：避免过度排除，只处理统计显著的情况
- **动态调整**：根据数据质量自动调整排除强度
- **兼容性好**：保持原有接口不变，无缝集成到现有系统

修改完成后，数字排除权重功能已经成功替代了数字和转移权重，为预测系统提供了更加智能和精准的排除机制。
