#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试数字转移预测逻辑
"""

import numpy as np

# 模拟数字转移预测功能
class DigitTransitionTester:
    def __init__(self):
        self.digit_transition_probs = None
        
    def fit(self, history_data):
        history = np.array(history_data)
        n_periods, n_positions = history.shape
        
        # 初始化10x10的转移概率矩阵，每个位置一个
        self.digit_transition_probs = np.zeros((n_positions, 10, 10))

        for pos in range(n_positions):
            # 对于每个位置，统计当前数字到下一期数字的转移频率
            for i in range(n_periods - 1):
                current_digit = history[i, pos]
                next_digit = history[i + 1, pos]
                self.digit_transition_probs[pos, current_digit, next_digit] += 1

            # 归一化得到概率
            for d in range(10):
                row_sum = np.sum(self.digit_transition_probs[pos, d])
                if row_sum > 0:
                    self.digit_transition_probs[pos, d] /= row_sum
                else:
                    # 如果某个数字从未出现，使用均匀分布
                    self.digit_transition_probs[pos, d] = np.ones(10) / 10
    
    def predict(self, position, last_digit):
        """预测某位置下期数字概率分布"""
        return self.digit_transition_probs[position, last_digit]

# 创建测试数据
test_data = [
    [7, 1, 2, 4, 6],  # 第1期
    [3, 5, 8, 2, 1],  # 第2期
    [9, 0, 4, 7, 3],  # 第3期
    [2, 6, 1, 8, 5],  # 第4期
    [4, 3, 9, 0, 7],  # 第5期
    [1, 8, 5, 3, 2],  # 第6期
    [6, 2, 7, 9, 1],  # 第7期
    [8, 4, 0, 5, 6],  # 第8期
    [3, 7, 2, 1, 9],  # 第9期
    [5, 1, 6, 4, 8],  # 第10期
]

print('🎯 数字转移预测逻辑测试')
print('=' * 50)

# 创建并训练模型
tester = DigitTransitionTester()
tester.fit(test_data)

print('📊 测试数据:')
for i, period in enumerate(test_data):
    print(f'第{i+1:2d}期: {period}')

print(f'\n✅ 模型训练完成')
print(f'矩阵维度: {tester.digit_transition_probs.shape}')
print('维度说明: [5个位置][10个当前数字][10个下期数字]')

# 手动验证位置0的转移统计
print('\n📈 位置0的转移统计验证:')
print('-' * 30)

pos0_transitions = {}
for i in range(len(test_data) - 1):
    current_digit = test_data[i][0]  # 当期位置0的数字
    next_digit = test_data[i + 1][0]  # 下期位置0的数字
    
    if current_digit not in pos0_transitions:
        pos0_transitions[current_digit] = {}
    if next_digit not in pos0_transitions[current_digit]:
        pos0_transitions[current_digit][next_digit] = 0
    pos0_transitions[current_digit][next_digit] += 1
    
    print(f'第{i+1}期位置0数字{current_digit} → 第{i+2}期位置0数字{next_digit}')

print('\n手动统计结果:')
for current_digit in sorted(pos0_transitions.keys()):
    transitions = pos0_transitions[current_digit]
    total = sum(transitions.values())
    print(f'数字{current_digit}: ', end='')
    for next_digit in sorted(transitions.keys()):
        count = transitions[next_digit]
        prob = count / total * 100
        print(f'→{next_digit}({count}次,{prob:.1f}%) ', end='')
    print()

# 验证模型的转移概率矩阵
print('\n模型转移概率矩阵验证:')
for current_digit in sorted(pos0_transitions.keys()):
    model_probs = tester.digit_transition_probs[0, current_digit]  # 位置0，当前数字
    non_zero_probs = [(d, p) for d, p in enumerate(model_probs) if p > 0]
    
    if non_zero_probs:
        print(f'数字{current_digit}: ', end='')
        for next_digit, prob in non_zero_probs:
            print(f'→{next_digit}({prob:.1%}) ', end='')
        print()

print('\n🎯 预测功能测试')
print('-' * 30)

last_period = test_data[-1]  # [5, 1, 6, 4, 8]
print(f'上期数据: {last_period}')

print('\n各位置预测结果:')
for pos in range(5):
    last_digit = last_period[pos]
    prediction = tester.predict(pos, last_digit)
    
    print(f'位置{pos}(上期数字{last_digit}): ', end='')
    
    # 显示非零概率
    non_zero = [(d, p) for d, p in enumerate(prediction) if p > 0]
    for digit, prob in non_zero:
        print(f'{digit}({prob:.1%}) ', end='')
    print()

print('\n🔍 详细分析示例 - 位置0')
print('-' * 30)

pos = 0
last_digit = last_period[pos]  # 位置0上期数字是5
print(f'分析位置{pos}，上期数字{last_digit}')

# 查看历史上数字5在位置0后面跟什么数字
print('历史上数字5在位置0的转移记录:')
for i in range(len(test_data) - 1):
    if test_data[i][pos] == last_digit:
        next_digit = test_data[i + 1][pos]
        print(f'  第{i+1}期位置{pos}数字{last_digit} → 第{i+2}期位置{pos}数字{next_digit}')

# 显示模型的预测概率
prediction = tester.predict(pos, last_digit)
print(f'模型预测概率分布:')
for d in range(10):
    if prediction[d] > 0:
        print(f'  数字{d}: {prediction[d]:.1%}')

print('\n✅ 数字转移预测逻辑测试完成！')
print('\n📋 逻辑总结:')
print('1. 统计每个位置上，当前数字X转移到下期数字Y的频率')
print('2. 归一化为概率分布')
print('3. 预测时，根据上期该位置的数字，查询对应的转移概率')
print('4. 这就是：本期某位置的数字X，预测下期同一位置会出现什么数字')
