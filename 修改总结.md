# 代码修改总结

## 修改目标
根据用户要求，删除短期、中期、长期权重，增加一个每期各个位置数字和的转移权重，即基于本期的和与下期的和关系统计。

## 主要修改内容

### 1. 配置文件修改 (DEFAULT_CONFIG)
**删除的参数：**
- `short_weight`: 短期预测权重
- `long_weight`: 长期预测权重
- `mid_weight`: 中期预测权重（在代码中动态添加的）

**保留的参数：**
- `co_weight`: 协同预测权重 (0.4)
- `digit_transition_weight`: 数字转移权重 (0.3)

**新增的参数：**
- `sum_transition_weight`: 每期数字和转移权重 (0.3)

### 2. 模型类 (MFTNModel) 修改

#### 2.1 初始化方法
- 删除了 `mid_weight` 的默认设置
- 添加了 `sum_transition_weight` 的默认设置
- 新增了 `sum_transition_probs` 属性用于存储数字和转移概率矩阵

#### 2.2 训练方法 (fit)
- 添加了 `_build_sum_transition_matrix()` 调用来构建数字和转移矩阵

#### 2.3 预测方法 (predict_next)
**删除的预测方法调用：**
- `_short_term_predict(pos)`
- `_mid_term_predict(pos)`
- `_long_term_predict(pos)`

**保留的预测方法调用：**
- `_co_predict(pos)`: 协同预测
- `_digit_transition_predict(pos)`: 数字转移预测

**新增的预测方法调用：**
- `_sum_transition_predict(pos)`: 数字和转移预测

**权重计算修改：**
- 现在只使用三种权重：`co_weight`、`digit_transition_weight`、`sum_transition_weight`
- 权重归一化确保总和为1

#### 2.4 删除的预测方法
- `_short_term_predict()`: 短期动态预测
- `_mid_term_predict()`: 中期趋势预测
- `_long_term_predict()`: 长期规律建模

#### 2.5 新增的预测方法

**`_build_sum_transition_matrix()`**
- 计算每期的数字和（5个位置数字之和）
- 构建从当前期数字和到下一期各位置数字的转移概率矩阵
- 矩阵维度：(5个位置, 46个可能的和值(0-45), 10个数字)

**`_sum_transition_predict(position)`**
- 基于上一期的数字和，预测当前位置的数字概率分布
- 使用构建好的数字和转移概率矩阵进行预测

### 3. 参数优化器 (ParameterOptimizer) 修改

#### 3.1 参数搜索范围
**删除的参数：**
- `short_weight`
- `long_weight`
- `mid_weight`

**保留的参数：**
- `co_weight`: (0.0, 1.0)
- `digit_transition_weight`: (0.0, 1.0)

**新增的参数：**
- `sum_transition_weight`: (0.0, 1.0)

#### 3.2 权重约束检查
- 更新了所有权重约束检查，现在只检查三个权重的总和是否为1
- 更新了权重正则化方法
- 更新了随机参数生成、交叉、变异等方法

### 4. GUI界面修改

#### 4.1 参数显示
- 更新了关键参数显示，现在显示：`lambda`、`co_weight`、`digit_transition_weight`、`sum_transition_weight`

#### 4.2 参数设置对话框
- 更新了参数标签，删除了短期、中期、长期权重的设置
- 添加了数字和转移权重的设置
- 更新了权重验证逻辑

#### 4.3 回测评估
- 更新了权重归一化逻辑，现在只处理三个权重参数

## 核心算法改进

### 数字和转移权重的工作原理

1. **数据统计**：
   - 计算历史数据中每期的数字和（5个位置数字相加）
   - 统计从当前期数字和到下一期各位置数字的转移频率

2. **概率矩阵构建**：
   - 为每个位置构建一个 46×10 的转移概率矩阵
   - 行索引：当前期数字和（0-45）
   - 列索引：下一期该位置的数字（0-9）

3. **预测应用**：
   - 获取上一期的数字和
   - 查询对应的转移概率分布
   - 作为该位置的预测概率之一

### 权重融合策略

现在的预测结果由三部分组成：
1. **协同预测** (co_weight = 0.4)：基于位置间相关性的预测
2. **数字转移预测** (digit_transition_weight = 0.3)：基于单个数字转移的预测
3. **数字和转移预测** (sum_transition_weight = 0.3)：基于整体数字和转移的预测

## 预期效果

1. **更全面的特征捕获**：数字和转移权重能够捕获整体数字分布的变化趋势
2. **简化模型结构**：删除了复杂的短期、中期、长期预测，使模型更加简洁
3. **保持预测能力**：通过数字和转移权重补偿了删除预测方法的影响
4. **更好的泛化能力**：数字和特征具有更强的统计稳定性

## 使用说明

修改后的代码保持了原有的接口不变，用户可以：
1. 正常加载和使用模型
2. 在参数设置中调整三个权重参数
3. 进行自动参数优化
4. 查看预测结果和回测分析

所有原有功能都得到保留，只是内部预测机制发生了变化。
