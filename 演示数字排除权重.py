#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示数字排除权重的工作原理
"""

import numpy as np

def demonstrate_digit_exclusion():
    """演示数字排除权重的工作原理"""
    print("=" * 80)
    print("数字排除权重工作原理演示")
    print("=" * 80)
    
    try:
        from all import MFTNModel
        
        # 1. 创建有规律的测试数据
        print("\n📊 第一步：创建测试数据")
        print("-" * 50)
        
        # 创建200期数据，建立明确的排除规律
        test_data = []
        np.random.seed(42)
        
        print("创建具有排除规律的测试数据...")
        print("规律1：如果本期位置0是数字7，那么下期位置1和位置3很少出现7")
        print("规律2：如果本期位置2是数字3，那么下期位置0和位置4很少出现3")
        
        for i in range(200):
            period = np.random.randint(0, 10, size=5)
            
            # 建立规律1：位置0的数字7排除下期位置1和3
            if i > 0:
                prev_period = test_data[i-1]
                if prev_period[0] == 7:  # 如果上期位置0是7
                    # 下期位置1和3避免出现7（90%概率避免）
                    if np.random.random() < 0.9:
                        while period[1] == 7:
                            period[1] = np.random.randint(0, 7)
                    if np.random.random() < 0.9:
                        while period[3] == 7:
                            period[3] = np.random.randint(0, 7)
                
                # 建立规律2：位置2的数字3排除下期位置0和4
                if prev_period[2] == 3:
                    if np.random.random() < 0.85:
                        while period[0] == 3:
                            period[0] = np.random.randint(4, 10)
                    if np.random.random() < 0.85:
                        while period[4] == 3:
                            period[4] = np.random.randint(4, 10)
            
            test_data.append(period)
        
        test_data = np.array(test_data)
        print(f"✓ 创建了 {len(test_data)} 期测试数据")
        print(f"  前5期数据示例：")
        for i in range(5):
            print(f"    第{i+1}期: {test_data[i]}")
        
        # 2. 训练模型并构建排除矩阵
        print("\n🔧 第二步：构建数字排除矩阵")
        print("-" * 50)
        
        model = MFTNModel()
        model.fit(test_data)
        
        if model.digit_exclusion_matrix is not None:
            print("✓ 数字排除矩阵构建成功")
            print(f"  矩阵维度: {model.digit_exclusion_matrix.shape}")
            print("  矩阵含义: [当前位置][数字][下期位置] = 排除权重")
        else:
            print("✗ 数字排除矩阵构建失败")
            return
        
        # 3. 分析发现的排除规律
        print("\n🔍 第三步：分析发现的排除规律")
        print("-" * 50)
        
        # 检查位置0的数字7对其他位置的影响
        exclusion_weights_7 = model.digit_exclusion_matrix[0, 7, :]
        print("位置0的数字7对各位置的排除权重:")
        for pos in range(5):
            weight = exclusion_weights_7[pos]
            if weight < 1.0:
                print(f"  位置{pos}: {weight:.2f} ← 被排除（权重减小）")
            else:
                print(f"  位置{pos}: {weight:.2f}")
        
        # 检查位置2的数字3对其他位置的影响
        exclusion_weights_3 = model.digit_exclusion_matrix[2, 3, :]
        print("\n位置2的数字3对各位置的排除权重:")
        for pos in range(5):
            weight = exclusion_weights_3[pos]
            if weight < 1.0:
                print(f"  位置{pos}: {weight:.2f} ← 被排除（权重减小）")
            else:
                print(f"  位置{pos}: {weight:.2f}")
        
        # 4. 演示预测过程
        print("\n🎯 第四步：演示预测过程")
        print("-" * 50)
        
        # 设置特定的测试场景
        test_scenarios = [
            [7, 1, 2, 4, 6],  # 位置0是7，应该影响位置1和3
            [2, 5, 3, 8, 9],  # 位置2是3，应该影响位置0和4
        ]
        
        for scenario_idx, test_period in enumerate(test_scenarios):
            print(f"\n场景 {scenario_idx + 1}: 上期数据 {test_period}")
            
            # 设置测试数据
            model.history = np.vstack([model.history, np.array(test_period).reshape(1, -1)])
            
            # 分析每个位置的排除效果
            for pos in range(5):
                print(f"\n  预测位置 {pos}:")
                
                # 获取排除预测概率
                exclusion_pred = model._digit_exclusion_predict(pos)
                
                # 显示概率分布
                print("    数字概率分布:")
                for digit in range(10):
                    prob = exclusion_pred[digit]
                    if prob < 0.08:  # 被明显排除的数字
                        print(f"      数字{digit}: {prob:.3f} ← 被排除")
                    elif prob > 0.12:  # 概率较高的数字
                        print(f"      数字{digit}: {prob:.3f}")
                    else:
                        print(f"      数字{digit}: {prob:.3f}")
                
                # 分析排除原因
                print("    排除分析:")
                for source_pos in range(5):
                    source_digit = test_period[source_pos]
                    exclusion_weight = model.digit_exclusion_matrix[source_pos, source_digit, pos]
                    if exclusion_weight < 1.0:
                        print(f"      位置{source_pos}的数字{source_digit} → 位置{pos}权重{exclusion_weight:.2f}")
        
        # 5. 对比不同权重的效果
        print("\n⚖️ 第五步：对比不同排除权重的效果")
        print("-" * 50)
        
        # 测试不同的排除权重值
        test_weights = [0.0, 0.1, 0.3, 0.5, 1.0]
        test_period = [7, 1, 2, 4, 6]
        
        print(f"测试场景：上期数据 {test_period}")
        print("预测位置1（应该排除数字7）:")
        
        for weight in test_weights:
            # 创建新模型并设置权重
            test_model = MFTNModel()
            test_model.params['digit_exclusion_weight'] = weight
            test_model.fit(test_data)
            test_model.history = np.vstack([test_model.history, np.array(test_period).reshape(1, -1)])
            
            # 进行预测
            predictions = test_model.predict_next(is_predict=True)
            
            # 检查数字7是否被排除
            if 1 in predictions and 7 in predictions[1]:
                exclusion_status = "未排除"
            else:
                exclusion_status = "已排除"
            
            print(f"  排除权重 {weight}: 数字7 {exclusion_status}")
            if 1 in predictions:
                print(f"    预测数字: {predictions[1]}")
        
        print("\n🎉 数字排除权重演示完成！")
        print("\n总结:")
        print("✓ 数字排除权重通过统计历史数据发现排除规律")
        print("✓ 智能识别最不可能出现的数字位置组合")
        print("✓ 减小（而非完全排除）不太可能数字的权重")
        print("✓ 提高预测的精准度和命中率")
        
        return True
        
    except Exception as e:
        print(f"✗ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = demonstrate_digit_exclusion()
    
    if success:
        print("\n" + "=" * 80)
        print("🎯 数字排除权重工作原理:")
        print("1. 统计分析：分析历史数据中数字的转移规律")
        print("2. 识别排除：找出最不可能出现的位置组合")
        print("3. 智能排除：减小权重而非完全排除")
        print("4. 预测应用：在预测时应用排除权重")
        print("5. 效果优化：提高预测精准度和命中率")
        print("=" * 80)
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
