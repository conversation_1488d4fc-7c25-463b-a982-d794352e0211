#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的代码是否正常工作
"""

import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from all import MFTNModel, DEFAULT_CONFIG
    print("✓ 成功导入模块")
except ImportError as e:
    print(f"✗ 导入模块失败: {e}")
    sys.exit(1)

def test_config():
    """测试配置"""
    print("\n=== 测试配置 ===")
    print("默认配置:")
    for key, value in DEFAULT_CONFIG.items():
        print(f"  {key}: {value}")
    
    # 检查是否包含新的权重参数
    required_weights = ['co_weight', 'digit_transition_weight', 'sum_transition_weight']
    missing_weights = [w for w in required_weights if w not in DEFAULT_CONFIG]
    
    if missing_weights:
        print(f"✗ 缺少权重参数: {missing_weights}")
        return False
    else:
        print("✓ 所有必需的权重参数都存在")
    
    # 检查权重之和
    weight_sum = sum(DEFAULT_CONFIG[w] for w in required_weights)
    print(f"权重之和: {weight_sum}")
    
    if abs(weight_sum - 1.0) < 0.01:
        print("✓ 权重之和接近1.0")
        return True
    else:
        print("✗ 权重之和不等于1.0")
        return False

def test_model_initialization():
    """测试模型初始化"""
    print("\n=== 测试模型初始化 ===")
    try:
        model = MFTNModel()
        print("✓ 模型初始化成功")
        
        # 检查新的属性
        if hasattr(model, 'sum_transition_probs'):
            print("✓ sum_transition_probs 属性存在")
        else:
            print("✗ sum_transition_probs 属性不存在")
            return False
            
        # 检查参数
        required_params = ['co_weight', 'digit_transition_weight', 'sum_transition_weight']
        for param in required_params:
            if param in model.params:
                print(f"✓ 参数 {param} 存在: {model.params[param]}")
            else:
                print(f"✗ 参数 {param} 不存在")
                return False
        
        return True
    except Exception as e:
        print(f"✗ 模型初始化失败: {e}")
        return False

def test_model_training():
    """测试模型训练"""
    print("\n=== 测试模型训练 ===")
    try:
        # 创建测试数据
        np.random.seed(42)
        test_data = np.random.randint(0, 10, size=(100, 5))
        print(f"创建测试数据: {test_data.shape}")
        
        model = MFTNModel()
        model.fit(test_data)
        print("✓ 模型训练成功")
        
        # 检查转移矩阵是否构建
        if model.digit_transition_probs is not None:
            print(f"✓ 数字转移矩阵构建成功: {model.digit_transition_probs.shape}")
        else:
            print("✗ 数字转移矩阵未构建")
            return False
            
        if model.sum_transition_probs is not None:
            print(f"✓ 数字和转移矩阵构建成功: {model.sum_transition_probs.shape}")
        else:
            print("✗ 数字和转移矩阵未构建")
            return False
        
        return True
    except Exception as e:
        print(f"✗ 模型训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_prediction():
    """测试预测功能"""
    print("\n=== 测试预测功能 ===")
    try:
        # 创建测试数据
        np.random.seed(42)
        test_data = np.random.randint(0, 10, size=(100, 5))
        
        model = MFTNModel()
        model.fit(test_data)
        
        # 进行预测
        predictions = model.predict_next(is_predict=True)
        print(f"✓ 预测成功，结果: {predictions}")
        
        # 检查预测结果格式
        if isinstance(predictions, dict) and len(predictions) == 5:
            print("✓ 预测结果格式正确")
            for pos, pred in predictions.items():
                if isinstance(pred, list) and len(pred) > 0:
                    print(f"  位置 {pos}: {pred}")
                else:
                    print(f"✗ 位置 {pos} 预测结果格式错误: {pred}")
                    return False
        else:
            print(f"✗ 预测结果格式错误: {predictions}")
            return False
        
        return True
    except Exception as e:
        print(f"✗ 预测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试修改后的代码...")
    
    tests = [
        ("配置测试", test_config),
        ("模型初始化测试", test_model_initialization),
        ("模型训练测试", test_model_training),
        ("预测功能测试", test_prediction)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"运行测试: {test_name}")
        print('='*50)
        
        if test_func():
            print(f"✓ {test_name} 通过")
            passed += 1
        else:
            print(f"✗ {test_name} 失败")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！代码修改成功！")
        return True
    else:
        print("❌ 部分测试失败，需要检查代码")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
