#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修正后的数字排除权重逻辑
"""

import numpy as np

def test_corrected_digit_exclusion():
    """测试修正后的数字排除权重逻辑"""
    print("=" * 80)
    print("测试修正后的数字排除权重逻辑")
    print("=" * 80)
    
    try:
        from all import MFTNModel
        
        # 1. 创建有明确规律的测试数据
        print("\n📊 第一步：创建测试数据")
        print("-" * 50)
        
        test_data = []
        np.random.seed(42)
        
        print("创建具有明确排除规律的测试数据...")
        print("规律1：数字7在位置1和位置3很少出现")
        print("规律2：数字3在位置0和位置4很少出现")
        print("规律3：数字5在位置2和位置3很少出现")
        
        for i in range(300):
            period = np.random.randint(0, 10, size=5)
            
            # 建立规律：某些数字在某些位置很少出现
            # 数字7在位置1和3很少出现（90%概率避免）
            if np.random.random() < 0.9:
                while period[1] == 7:
                    period[1] = np.random.randint(0, 7)
            if np.random.random() < 0.9:
                while period[3] == 7:
                    period[3] = np.random.randint(0, 7)
            
            # 数字3在位置0和4很少出现（85%概率避免）
            if np.random.random() < 0.85:
                while period[0] == 3:
                    period[0] = np.random.randint(4, 10)
            if np.random.random() < 0.85:
                while period[4] == 3:
                    period[4] = np.random.randint(4, 10)
            
            # 数字5在位置2和3很少出现（80%概率避免）
            if np.random.random() < 0.8:
                while period[2] == 5:
                    period[2] = np.random.randint(0, 5) if np.random.random() < 0.5 else np.random.randint(6, 10)
            if np.random.random() < 0.8:
                while period[3] == 5:
                    period[3] = np.random.randint(0, 5) if np.random.random() < 0.5 else np.random.randint(6, 10)
            
            test_data.append(period)
        
        test_data = np.array(test_data)
        print(f"✓ 创建了 {len(test_data)} 期测试数据")
        
        # 统计验证规律
        print("\n验证数据中的规律:")
        digit_7_positions = []
        digit_3_positions = []
        digit_5_positions = []
        
        for period in test_data:
            for pos, digit in enumerate(period):
                if digit == 7:
                    digit_7_positions.append(pos)
                elif digit == 3:
                    digit_3_positions.append(pos)
                elif digit == 5:
                    digit_5_positions.append(pos)
        
        print(f"数字7在各位置的分布: {np.bincount(digit_7_positions, minlength=5)}")
        print(f"数字3在各位置的分布: {np.bincount(digit_3_positions, minlength=5)}")
        print(f"数字5在各位置的分布: {np.bincount(digit_5_positions, minlength=5)}")
        
        # 2. 训练模型并构建排除矩阵
        print("\n🔧 第二步：构建数字排除矩阵")
        print("-" * 50)
        
        model = MFTNModel()
        model.fit(test_data)
        
        if model.digit_exclusion_matrix is not None:
            print("✓ 数字排除矩阵构建成功")
            print(f"  矩阵维度: {model.digit_exclusion_matrix.shape}")
            print("  矩阵含义: [数字][位置] = 排除权重")
        else:
            print("✗ 数字排除矩阵构建失败")
            return False
        
        # 3. 分析发现的排除规律
        print("\n🔍 第三步：分析发现的排除规律")
        print("-" * 50)
        
        # 检查数字7的排除权重
        exclusion_weights_7 = model.digit_exclusion_matrix[7, :]
        print("数字7在各位置的排除权重:")
        for pos in range(5):
            weight = exclusion_weights_7[pos]
            if weight < 1.0:
                print(f"  位置{pos}: {weight:.2f} ← 被排除（权重减小）")
            else:
                print(f"  位置{pos}: {weight:.2f}")
        
        # 检查数字3的排除权重
        exclusion_weights_3 = model.digit_exclusion_matrix[3, :]
        print("\n数字3在各位置的排除权重:")
        for pos in range(5):
            weight = exclusion_weights_3[pos]
            if weight < 1.0:
                print(f"  位置{pos}: {weight:.2f} ← 被排除（权重减小）")
            else:
                print(f"  位置{pos}: {weight:.2f}")
        
        # 检查数字5的排除权重
        exclusion_weights_5 = model.digit_exclusion_matrix[5, :]
        print("\n数字5在各位置的排除权重:")
        for pos in range(5):
            weight = exclusion_weights_5[pos]
            if weight < 1.0:
                print(f"  位置{pos}: {weight:.2f} ← 被排除（权重减小）")
            else:
                print(f"  位置{pos}: {weight:.2f}")
        
        # 4. 演示预测过程
        print("\n🎯 第四步：演示预测过程")
        print("-" * 50)
        
        # 设置包含被排除数字的测试场景
        test_scenarios = [
            [7, 1, 2, 4, 6],  # 包含数字7，应该在位置1和3被排除
            [2, 5, 3, 8, 9],  # 包含数字3和5，应该分别被排除
            [7, 3, 5, 1, 2],  # 包含多个被排除的数字
        ]
        
        for scenario_idx, test_period in enumerate(test_scenarios):
            print(f"\n场景 {scenario_idx + 1}: 上期数据 {test_period}")
            print(f"  上期包含的数字: {sorted(set(test_period))}")
            
            # 设置测试数据
            model.history = np.vstack([model.history, np.array(test_period).reshape(1, -1)])
            
            # 分析每个位置的排除效果
            for pos in range(5):
                print(f"\n  预测位置 {pos}:")
                
                # 获取排除预测概率
                exclusion_pred = model._digit_exclusion_predict(pos)
                
                # 显示被排除的数字
                excluded_digits = []
                for digit in test_period:
                    if exclusion_pred[digit] < 0.08:  # 被明显排除的数字
                        excluded_digits.append(digit)
                
                if excluded_digits:
                    print(f"    被排除的数字: {excluded_digits}")
                    for digit in excluded_digits:
                        print(f"      数字{digit}: {exclusion_pred[digit]:.3f} (原权重: {model.digit_exclusion_matrix[digit, pos]:.2f})")
                else:
                    print("    没有数字被明显排除")
                
                # 显示概率最高的数字
                top_digits = np.argsort(exclusion_pred)[-3:][::-1]
                print(f"    概率最高的3个数字:")
                for digit in top_digits:
                    print(f"      数字{digit}: {exclusion_pred[digit]:.3f}")
        
        # 5. 验证排除效果
        print("\n✅ 第五步：验证排除效果")
        print("-" * 50)
        
        # 测试特定数字的排除效果
        test_cases = [
            ([7, 0, 0, 0, 0], 7, [1, 3]),  # 数字7应该在位置1和3被排除
            ([0, 0, 3, 0, 0], 3, [0, 4]),  # 数字3应该在位置0和4被排除
            ([0, 0, 5, 0, 0], 5, [2, 3]),  # 数字5应该在位置2和3被排除
        ]
        
        for test_period, target_digit, expected_excluded_positions in test_cases:
            print(f"\n测试数字{target_digit}的排除效果:")
            print(f"  测试场景: {test_period}")
            
            model.history = np.vstack([model.history, np.array(test_period).reshape(1, -1)])
            
            excluded_positions = []
            for pos in expected_excluded_positions:
                exclusion_pred = model._digit_exclusion_predict(pos)
                if exclusion_pred[target_digit] < 0.08:
                    excluded_positions.append(pos)
                    print(f"    位置{pos}: 数字{target_digit}概率 {exclusion_pred[target_digit]:.3f} ✓ 被排除")
                else:
                    print(f"    位置{pos}: 数字{target_digit}概率 {exclusion_pred[target_digit]:.3f} ✗ 未被排除")
            
            if set(excluded_positions) == set(expected_excluded_positions):
                print(f"  ✓ 排除效果正确")
            else:
                print(f"  ✗ 排除效果不符合预期")
        
        print("\n🎉 修正后的数字排除权重测试完成！")
        return True
        
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_corrected_digit_exclusion()
    
    if success:
        print("\n" + "=" * 80)
        print("🎯 修正后的数字排除权重逻辑:")
        print("1. 统计每个数字在各位置的历史出现频率")
        print("2. 找出每个数字最不可能出现的两个位置")
        print("3. 对这些位置设置排除权重（减小到30%）")
        print("4. 预测时根据上期的具体数字应用排除权重")
        print("5. 例如：上期有数字7，则在7最不可能的位置减小7的权重")
        print("=" * 80)
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
