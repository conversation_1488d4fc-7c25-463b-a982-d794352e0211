#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试选择数量参数修复
"""

import numpy as np

def test_selection_count_parameter():
    """测试选择数量参数"""
    print("=== 测试选择数量参数 ===")
    
    try:
        from all import DEFAULT_CONFIG, MFTNModel, load_config
        
        # 1. 检查 DEFAULT_CONFIG
        print("1. 检查 DEFAULT_CONFIG:")
        if 'selection_count' in DEFAULT_CONFIG:
            print(f"  ✓ selection_count: {DEFAULT_CONFIG['selection_count']}")
        else:
            print("  ✗ selection_count 缺失")
            return False
        
        # 2. 检查配置文件
        print("\n2. 检查配置文件:")
        config = load_config()
        if 'selection_count' in config:
            print(f"  ✓ selection_count: {config['selection_count']}")
        else:
            print("  ✗ selection_count 缺失")
            return False
        
        # 3. 测试模型初始化
        print("\n3. 测试模型初始化:")
        model = MFTNModel()
        if 'selection_count' in model.params:
            print(f"  ✓ 模型中 selection_count: {model.params['selection_count']}")
        else:
            print("  ✗ 模型中 selection_count 缺失")
            return False
        
        # 4. 测试预测功能
        print("\n4. 测试预测功能:")
        
        # 创建测试数据
        np.random.seed(42)
        test_data = np.random.randint(0, 10, size=(50, 5))
        
        # 训练模型
        model.fit(test_data)
        print("  ✓ 模型训练成功")
        
        # 进行预测
        predictions = model.predict_next(is_predict=True)
        
        # 检查每个位置的预测数量
        expected_count = model.params['selection_count']
        print(f"  期望每位置选择数量: {expected_count}")
        
        all_correct = True
        for pos in range(5):
            if pos in predictions:
                actual_count = len(predictions[pos])
                print(f"  位置 {pos}: {actual_count} 个数字 {predictions[pos]}")
                if actual_count != expected_count:
                    print(f"    ✗ 数量不正确，期望 {expected_count}，实际 {actual_count}")
                    all_correct = False
                else:
                    print(f"    ✓ 数量正确")
            else:
                print(f"  ✗ 位置 {pos}: 缺失预测")
                all_correct = False
        
        if not all_correct:
            return False
        
        # 5. 测试不同的选择数量
        print("\n5. 测试不同的选择数量:")
        
        test_counts = [3, 5, 7, 8]
        for test_count in test_counts:
            print(f"\n  测试选择数量: {test_count}")
            
            # 创建新模型并设置选择数量
            test_model = MFTNModel()
            test_model.params['selection_count'] = test_count
            test_model.fit(test_data)
            
            # 进行预测
            test_predictions = test_model.predict_next(is_predict=True)
            
            # 检查结果
            success = True
            for pos in range(5):
                if pos in test_predictions:
                    actual_count = len(test_predictions[pos])
                    if actual_count == test_count:
                        print(f"    位置 {pos}: ✓ {actual_count} 个数字")
                    else:
                        print(f"    位置 {pos}: ✗ 期望 {test_count}，实际 {actual_count}")
                        success = False
                else:
                    print(f"    位置 {pos}: ✗ 缺失预测")
                    success = False
            
            if not success:
                return False
        
        print("\n🎉 选择数量参数测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_params_dialog_with_selection_count():
    """测试参数对话框包含选择数量参数"""
    print("\n=== 测试参数对话框包含选择数量参数 ===")
    
    try:
        from all import DEFAULT_CONFIG, load_config
        
        # 1. 检查参数标签
        param_labels = {
            'alpha': '平滑因子 (alpha):',
            'lambda': '冷号衰减系数 (lambda):',
            'co_weight': '协同预测权重:',
            'digit_transition_weight': '数字转移权重:',
            'digit_exclusion_weight': '数字排除权重:',
            'hot_threshold': '热号阈值:',
            'cold_threshold': '冷号阈值:',
            'selection_count': '每位置选择数量:',
            'window': '窗口大小:',
            'periodicity': '周期特征期数:'
        }
        
        print("1. 检查参数标签:")
        if 'selection_count' in param_labels:
            print(f"  ✓ selection_count 标签: {param_labels['selection_count']}")
        else:
            print("  ✗ selection_count 标签缺失")
            return False
        
        # 2. 检查参数可用性
        print("\n2. 检查参数可用性:")
        config = load_config()
        
        missing_params = []
        for param in param_labels.keys():
            if param in config:
                print(f"  ✓ {param}: {config[param]}")
            else:
                print(f"  ✗ {param}: 缺失")
                missing_params.append(param)
        
        if missing_params:
            print(f"  缺失参数: {missing_params}")
            return False
        
        # 3. 检查参数类型
        print("\n3. 检查参数类型:")
        integer_params = ['selection_count', 'window', 'periodicity']
        
        for param in integer_params:
            value = config[param]
            if isinstance(value, int) or (isinstance(value, float) and value.is_integer()):
                print(f"  ✓ {param}: {value} (整数)")
            else:
                print(f"  ✗ {param}: {value} (非整数)")
                return False
        
        print("\n🎉 参数对话框测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def update_config_with_selection_count():
    """更新配置文件，确保包含选择数量参数"""
    print("\n=== 更新配置文件 ===")
    
    try:
        from all import load_config, save_config, DEFAULT_CONFIG
        
        # 加载当前配置
        config = load_config()
        
        # 检查是否需要添加 selection_count
        if 'selection_count' not in config:
            print("  添加缺失的 selection_count 参数")
            config['selection_count'] = DEFAULT_CONFIG['selection_count']
            
            # 保存配置
            if save_config(config):
                print("  ✓ 配置已更新")
            else:
                print("  ✗ 配置更新失败")
                return False
        else:
            print(f"  ✓ selection_count 已存在: {config['selection_count']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 更新配置失败: {e}")
        return False

def main():
    """主函数"""
    print("开始测试选择数量参数修复...")
    print("="*60)
    
    success = True
    
    # 更新配置文件
    if not update_config_with_selection_count():
        success = False
    
    # 测试选择数量参数
    if not test_selection_count_parameter():
        success = False
    
    # 测试参数对话框
    if not test_params_dialog_with_selection_count():
        success = False
    
    print("\n" + "="*60)
    if success:
        print("🎉 选择数量参数修复完成！")
        print("\n修复内容:")
        print("✓ 在 DEFAULT_CONFIG 中添加了 selection_count 参数")
        print("✓ 在参数设置对话框中添加了选择数量参数")
        print("✓ 更新了参数类型检查逻辑")
        print("✓ 验证了预测功能正常工作")
        print("✓ 测试了不同选择数量的效果")
        print(f"\n现在每个位置将选择 {DEFAULT_CONFIG['selection_count']} 个数字，而不是默认的2个！")
    else:
        print("❌ 选择数量参数修复失败")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
