#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试参数设置对话框问题
"""

import json
import os

def debug_params_issue():
    """调试参数设置问题"""
    print("=== 调试参数设置对话框问题 ===")
    
    try:
        from all import DEFAULT_CONFIG, load_config, CONFIG_FILE
        
        # 1. 检查 DEFAULT_CONFIG
        print("1. 检查 DEFAULT_CONFIG:")
        for param, value in DEFAULT_CONFIG.items():
            print(f"  {param}: {value}")
        
        # 2. 检查配置文件
        print(f"\n2. 检查配置文件 ({CONFIG_FILE}):")
        if os.path.exists(CONFIG_FILE):
            print("  配置文件存在")
            try:
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                print("  配置文件内容:")
                for param, value in config_data.items():
                    print(f"    {param}: {value}")
            except Exception as e:
                print(f"  读取配置文件失败: {e}")
        else:
            print("  配置文件不存在")
        
        # 3. 检查 load_config() 结果
        print("\n3. 检查 load_config() 结果:")
        loaded_config = load_config()
        for param, value in loaded_config.items():
            print(f"  {param}: {value}")
        
        # 4. 模拟应用初始化
        print("\n4. 模拟应用初始化:")
        config = load_config()
        default_params = {k: v for k, v in config.items() if k != 'last_excel_file'}
        print("  default_params:")
        for param, value in default_params.items():
            print(f"    {param}: {value}")
        
        # 5. 检查参数对话框需要的参数
        print("\n5. 检查参数对话框需要的参数:")
        param_labels = {
            'alpha': '平滑因子 (alpha):',
            'lambda': '冷号衰减系数 (lambda):',
            'co_weight': '协同预测权重:',
            'digit_transition_weight': '数字转移权重:',
            'digit_exclusion_weight': '数字排除权重:',
            'hot_threshold': '热号阈值:',
            'cold_threshold': '冷号阈值:',
            'window': '窗口大小:',
            'periodicity': '周期特征期数:'
        }
        
        missing_params = []
        for param in param_labels.keys():
            if param in default_params:
                print(f"  ✓ {param}: {default_params[param]}")
            else:
                print(f"  ✗ {param}: 缺失")
                missing_params.append(param)
        
        if missing_params:
            print(f"\n❌ 发现缺失参数: {missing_params}")
            
            # 6. 修复缺失参数
            print("\n6. 修复缺失参数:")
            fixed_config = loaded_config.copy()
            for param in missing_params:
                if param in DEFAULT_CONFIG:
                    fixed_config[param] = DEFAULT_CONFIG[param]
                    print(f"  添加 {param}: {DEFAULT_CONFIG[param]}")
            
            # 保存修复后的配置
            from all import save_config
            if save_config(fixed_config):
                print("  ✓ 修复后的配置已保存")
            else:
                print("  ✗ 保存修复后的配置失败")
        else:
            print("\n✅ 所有参数都存在")
        
        return len(missing_params) == 0
        
    except Exception as e:
        print(f"✗ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_params_dialog_creation():
    """测试参数对话框创建"""
    print("\n=== 测试参数对话框创建 ===")
    
    try:
        import tkinter as tk
        from all import DEFAULT_CONFIG
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 模拟参数对话框创建过程
        dialog = tk.Toplevel(root)
        dialog.title("测试参数设置")
        dialog.geometry("500x600")
        dialog.withdraw()  # 隐藏对话框
        
        # 模拟参数输入框创建
        param_entries = {}
        param_labels = {
            'alpha': '平滑因子 (alpha):',
            'lambda': '冷号衰减系数 (lambda):',
            'co_weight': '协同预测权重:',
            'digit_transition_weight': '数字转移权重:',
            'digit_exclusion_weight': '数字排除权重:',
            'hot_threshold': '热号阈值:',
            'cold_threshold': '冷号阈值:',
            'window': '窗口大小:',
            'periodicity': '周期特征期数:'
        }
        
        # 模拟获取当前参数
        current_params = DEFAULT_CONFIG.copy()
        
        frame = tk.Frame(dialog, padx=20, pady=10)
        frame.pack(fill=tk.BOTH, expand=True)
        
        success_count = 0
        for i, (param, label_text) in enumerate(param_labels.items()):
            try:
                # 创建标签
                label = tk.Label(frame, text=label_text, font=("SimHei", 10))
                label.grid(row=i, column=0, sticky=tk.W, pady=5)
                
                # 创建输入框
                if param in ['window', 'periodicity']:
                    var = tk.IntVar(value=int(current_params[param]))
                else:
                    var = tk.DoubleVar(value=current_params[param])
                entry = tk.Entry(frame, textvariable=var, width=10)
                entry.grid(row=i, column=1, sticky=tk.W, pady=5)
                
                param_entries[param] = var
                success_count += 1
                print(f"  ✓ 成功创建 {param}: {label_text}")
                
            except Exception as e:
                print(f"  ✗ 创建 {param} 失败: {e}")
        
        # 清理
        dialog.destroy()
        root.destroy()
        
        print(f"\n创建结果: {success_count}/{len(param_labels)} 个参数成功创建")
        return success_count == len(param_labels)
        
    except Exception as e:
        print(f"  ✗ 测试对话框创建失败: {e}")
        return False

def main():
    """主函数"""
    print("开始调试参数设置对话框问题...")
    print("="*60)
    
    # 调试参数问题
    params_ok = debug_params_issue()
    
    # 测试对话框创建
    dialog_ok = test_params_dialog_creation()
    
    print("\n" + "="*60)
    if params_ok and dialog_ok:
        print("🎉 参数设置对话框调试完成，问题已修复！")
    else:
        print("❌ 参数设置对话框仍有问题需要进一步修复")
    
    return params_ok and dialog_ok

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
