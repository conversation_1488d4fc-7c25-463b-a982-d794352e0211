#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试修改后的代码
"""

import numpy as np

# 测试配置
print("=== 测试配置 ===")
try:
    from all import DEFAULT_CONFIG
    print("默认配置:")
    for key, value in DEFAULT_CONFIG.items():
        print(f"  {key}: {value}")
    
    # 检查权重
    weights = ['co_weight', 'digit_transition_weight', 'sum_transition_weight']
    weight_sum = sum(DEFAULT_CONFIG.get(w, 0) for w in weights)
    print(f"权重之和: {weight_sum}")
    
    if abs(weight_sum - 1.0) < 0.01:
        print("✓ 权重之和正确")
    else:
        print("✗ 权重之和不正确")
        
except Exception as e:
    print(f"配置测试失败: {e}")

# 测试模型
print("\n=== 测试模型 ===")
try:
    from all import MFTNModel
    
    # 创建模型
    model = MFTNModel()
    print("✓ 模型创建成功")
    
    # 创建测试数据
    np.random.seed(42)
    test_data = np.random.randint(0, 10, size=(50, 5))
    print(f"测试数据形状: {test_data.shape}")
    
    # 训练模型
    model.fit(test_data)
    print("✓ 模型训练成功")
    
    # 检查转移矩阵
    if model.sum_transition_probs is not None:
        print(f"✓ 数字和转移矩阵: {model.sum_transition_probs.shape}")
    else:
        print("✗ 数字和转移矩阵未创建")
    
    # 进行预测
    predictions = model.predict_next(is_predict=True)
    print(f"✓ 预测成功: {predictions}")
    
    print("🎉 所有测试通过！")
    
except Exception as e:
    print(f"模型测试失败: {e}")
    import traceback
    traceback.print_exc()
