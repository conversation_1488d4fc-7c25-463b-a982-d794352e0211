#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试每期数字和转移权重功能
"""

import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append('.')

def test_sum_transition_weight():
    """测试数字和转移权重功能"""
    print("🔍 测试每期数字和转移权重功能")
    print("=" * 50)
    
    try:
        # 导入模型类
        exec(open('all - 副本.py').read(), globals())
        
        # 创建测试数据
        test_data = [
            [7, 1, 2, 4, 6],  # 和值=20
            [3, 5, 8, 2, 1],  # 和值=19  
            [9, 0, 4, 7, 3],  # 和值=23
            [2, 6, 1, 8, 5],  # 和值=22
            [4, 3, 9, 0, 7],  # 和值=23
            [1, 8, 5, 3, 2],  # 和值=19
            [6, 4, 0, 9, 1],  # 和值=20
            [8, 2, 7, 1, 4],  # 和值=22
        ]
        
        print("📊 测试数据:")
        for i, data in enumerate(test_data):
            period_sum = sum(data)
            print(f"第{i+1}期: {data} → 数字和: {period_sum}")
        
        print("\n🔧 创建和训练模型...")
        
        # 创建模型
        model = MFTNModel()
        
        # 检查模型是否有sum_transition_weight参数
        if 'sum_transition_weight' not in model.params:
            print("❌ 模型缺少sum_transition_weight参数")
            return False
            
        print(f"✅ sum_transition_weight参数: {model.params['sum_transition_weight']}")
        
        # 训练模型
        model.fit(test_data)
        
        # 检查是否成功构建了数字和转移概率矩阵
        if not hasattr(model, 'sum_transition_probs'):
            print("❌ 模型没有sum_transition_probs属性")
            return False
            
        if model.sum_transition_probs is None:
            print("❌ sum_transition_probs矩阵为None")
            return False
            
        print(f"✅ 数字和转移概率矩阵构建成功")
        print(f"   矩阵维度: {model.sum_transition_probs.shape}")
        
        # 测试_sum_transition_predict方法
        print("\n🎯 测试预测功能...")
        
        last_period = test_data[-1]  # [8, 2, 7, 1, 4]
        last_sum = sum(last_period)  # 22
        print(f"上期数据: {last_period}")
        print(f"上期数字和: {last_sum}")
        print()
        
        # 测试每个位置的预测
        all_predictions_valid = True
        for pos in range(5):
            try:
                sum_pred = model._sum_transition_predict(pos)
                
                if sum_pred is None:
                    print(f"❌ 位置{pos}: 预测结果为None")
                    all_predictions_valid = False
                    continue
                    
                if len(sum_pred) != 10:
                    print(f"❌ 位置{pos}: 预测结果长度错误 {len(sum_pred)}")
                    all_predictions_valid = False
                    continue
                    
                prob_sum = np.sum(sum_pred)
                if abs(prob_sum - 1.0) > 0.01:
                    print(f"❌ 位置{pos}: 概率和不为1 ({prob_sum:.3f})")
                    all_predictions_valid = False
                    continue
                    
                print(f"✅ 位置{pos}: 预测正常")
                print(f"   概率分布: {[f'{p:.1%}' for p in sum_pred]}")
                
                # 找出最可能的数字
                max_prob_digit = np.argmax(sum_pred)
                max_prob = sum_pred[max_prob_digit]
                print(f"   最可能数字: {max_prob_digit} (概率: {max_prob:.1%})")
                print()
                
            except Exception as e:
                print(f"❌ 位置{pos}: 预测出错 - {str(e)}")
                all_predictions_valid = False
        
        if not all_predictions_valid:
            print("❌ 数字和转移预测功能存在问题")
            return False
            
        # 测试完整预测流程
        print("🔄 测试完整预测流程...")
        try:
            predictions = model.predict_next(is_predict=True)
            
            if not isinstance(predictions, dict):
                print(f"❌ 预测结果类型错误: {type(predictions)}")
                return False
                
            if len(predictions) != 5:
                print(f"❌ 预测结果位置数量错误: {len(predictions)}")
                return False
                
            print("✅ 完整预测流程正常")
            print("最终预测结果:")
            for pos in range(5):
                if pos in predictions:
                    pred_nums = predictions[pos]
                    print(f"  位置{pos}: {pred_nums}")
                else:
                    print(f"  ❌ 缺少位置{pos}的预测")
                    return False
                    
        except Exception as e:
            print(f"❌ 完整预测流程出错: {str(e)}")
            return False
        
        print("\n🎉 所有测试通过！数字和转移权重功能正常工作！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_sum_transition_weight()
    if success:
        print("\n✅ 数字和转移权重功能验证成功！")
    else:
        print("\n❌ 数字和转移权重功能存在问题！")
