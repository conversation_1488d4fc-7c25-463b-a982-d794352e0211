#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试滚动功能的简单脚本
"""

import tkinter as tk
from tkinter import ttk

def test_scroll_interface():
    """测试滚动界面"""
    root = tk.Tk()
    root.title("滚动功能测试")
    root.geometry("800x600")
    
    # 创建主滚动框架
    container = tk.Frame(root)
    container.pack(fill="both", expand=True)
    
    # 创建画布和滚动条
    canvas = tk.Canvas(container, bg="#f0f0f0")
    v_scrollbar = tk.Scrollbar(container, orient="vertical", command=canvas.yview)
    h_scrollbar = tk.Scrollbar(container, orient="horizontal", command=canvas.xview)
    scrollable_frame = tk.Frame(canvas, bg="#f0f0f0")
    
    # 配置滚动
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )
    
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
    
    # 布局滚动条和画布
    v_scrollbar.pack(side="right", fill="y")
    h_scrollbar.pack(side="bottom", fill="x")
    canvas.pack(side="left", fill="both", expand=True)
    
    # 绑定鼠标滚轮事件和键盘事件
    def _on_mousewheel(event):
        canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    
    def _on_shift_mousewheel(event):
        canvas.xview_scroll(int(-1*(event.delta/120)), "units")
    
    def _on_key_press(event):
        if event.keysym == 'Up':
            canvas.yview_scroll(-1, "units")
        elif event.keysym == 'Down':
            canvas.yview_scroll(1, "units")
        elif event.keysym == 'Left':
            canvas.xview_scroll(-1, "units")
        elif event.keysym == 'Right':
            canvas.xview_scroll(1, "units")
        elif event.keysym == 'Prior':  # Page Up
            canvas.yview_scroll(-10, "units")
        elif event.keysym == 'Next':   # Page Down
            canvas.yview_scroll(10, "units")
        elif event.keysym == 'Home':
            canvas.yview_moveto(0)
        elif event.keysym == 'End':
            canvas.yview_moveto(1)
    
    # 绑定事件
    canvas.bind("<MouseWheel>", _on_mousewheel)
    canvas.bind("<Shift-MouseWheel>", _on_shift_mousewheel)
    root.bind("<Key>", _on_key_press)
    
    # 让画布可以获得焦点以接收键盘事件
    canvas.focus_set()
    canvas.bind("<Button-1>", lambda e: canvas.focus_set())
    
    # 添加测试内容
    # 滚动提示
    tip_frame = tk.Frame(scrollable_frame, bg="#FFF3E0", relief=tk.RAISED, bd=1)
    tip_frame.pack(fill=tk.X, pady=5, padx=10)
    
    tip_text = ("💡 滚动测试: 使用鼠标滚轮上下滚动 | Shift+滚轮左右滚动 | "
               "方向键/Page Up/Page Down/Home/End键导航 | 点击界面获得焦点")
    tk.Label(tip_frame, text=tip_text, bg="#FFF3E0", fg="#E65100", 
            font=("SimHei", 9), wraplength=700).pack(pady=5, padx=10)
    
    # 添加大量按钮来测试滚动
    for row in range(20):
        row_frame = tk.Frame(scrollable_frame, bg="#f0f0f0")
        row_frame.pack(fill=tk.X, pady=2, padx=10)
        
        for col in range(10):
            btn_text = f"按钮 {row*10 + col + 1}"
            btn = tk.Button(row_frame, text=btn_text, 
                           command=lambda t=btn_text: print(f"点击了 {t}"),
                           font=("SimHei", 9), padx=10, pady=5)
            btn.pack(side=tk.LEFT, padx=2)
    
    # 添加一些长文本来测试水平滚动
    for i in range(5):
        long_text = f"这是一行很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长的文本 {i+1}"
        tk.Label(scrollable_frame, text=long_text, bg="#f0f0f0", 
                font=("SimHei", 10), anchor="w").pack(fill=tk.X, pady=2, padx=10)
    
    # 状态栏
    status_var = tk.StringVar()
    status_var.set("滚动功能测试 - 使用鼠标滚轮或键盘导航")
    status_bar = tk.Label(scrollable_frame, textvariable=status_var, bd=1, relief=tk.SUNKEN, 
                         anchor=tk.W, bg="#E3F2FD", fg="#1976D2", font=("SimHei", 9))
    status_bar.pack(side=tk.BOTTOM, fill=tk.X, pady=10, padx=10)
    
    print("滚动功能测试启动")
    print("- 使用鼠标滚轮进行垂直滚动")
    print("- 按住Shift键+鼠标滚轮进行水平滚动")
    print("- 使用方向键、Page Up/Down、Home/End键导航")
    print("- 点击界面获得键盘焦点")
    
    root.mainloop()

if __name__ == "__main__":
    test_scroll_interface()
