#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试数字转移预测逻辑是否正确
验证：本期某位置的数字X，预测下期同一位置会出现什么数字
"""

import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from all import MFTNModel
except ImportError:
    print("❌ 无法导入MFTNModel，请检查all.py文件是否存在")
    sys.exit(1)

def test_digit_transition_logic():
    """测试数字转移预测逻辑"""
    print("🎯 测试数字转移预测逻辑")
    print("=" * 60)
    
    # 创建测试数据
    test_data = [
        [7, 1, 2, 4, 6],  # 第1期
        [3, 5, 8, 2, 1],  # 第2期
        [9, 0, 4, 7, 3],  # 第3期
        [2, 6, 1, 8, 5],  # 第4期
        [4, 3, 9, 0, 7],  # 第5期
        [1, 8, 5, 3, 2],  # 第6期
        [6, 2, 7, 9, 1],  # 第7期
        [8, 4, 0, 5, 6],  # 第8期
        [3, 7, 2, 1, 9],  # 第9期
        [5, 1, 6, 4, 8],  # 第10期
    ]
    
    print("📊 测试数据：")
    for i, period in enumerate(test_data):
        print(f"第{i+1:2d}期: {period}")
    
    # 创建并训练模型
    model = MFTNModel()
    model.fit(test_data)
    
    print("\n🔧 数字转移矩阵构建验证")
    print("-" * 40)
    
    # 检查矩阵维度
    expected_shape = (5, 10, 10)  # [位置][当前数字][下期数字]
    actual_shape = model.digit_transition_probs.shape
    
    if actual_shape == expected_shape:
        print(f"✅ 矩阵维度正确: {actual_shape}")
    else:
        print(f"❌ 矩阵维度错误: 期望{expected_shape}, 实际{actual_shape}")
        return False
    
    print("   维度说明: [5个位置][10个当前数字][10个下期数字]")
    
    # 手动验证位置0的转移统计
    print("\n📈 位置0的转移统计验证")
    print("-" * 40)
    
    # 手动统计位置0的转移
    pos0_transitions = {}
    for i in range(len(test_data) - 1):
        current_digit = test_data[i][0]  # 当期位置0的数字
        next_digit = test_data[i + 1][0]  # 下期位置0的数字
        
        if current_digit not in pos0_transitions:
            pos0_transitions[current_digit] = {}
        if next_digit not in pos0_transitions[current_digit]:
            pos0_transitions[current_digit][next_digit] = 0
        pos0_transitions[current_digit][next_digit] += 1
        
        print(f"第{i+1}期位置0数字{current_digit} → 第{i+2}期位置0数字{next_digit}")
    
    print("\n手动统计结果:")
    for current_digit in sorted(pos0_transitions.keys()):
        transitions = pos0_transitions[current_digit]
        total = sum(transitions.values())
        print(f"数字{current_digit}: ", end="")
        for next_digit in sorted(transitions.keys()):
            count = transitions[next_digit]
            prob = count / total * 100
            print(f"→{next_digit}({count}次,{prob:.1f}%) ", end="")
        print()
    
    # 验证模型的转移概率矩阵
    print("\n模型转移概率矩阵验证:")
    for current_digit in sorted(pos0_transitions.keys()):
        model_probs = model.digit_transition_probs[0, current_digit]  # 位置0，当前数字
        non_zero_probs = [(d, p) for d, p in enumerate(model_probs) if p > 0]
        
        if non_zero_probs:
            print(f"数字{current_digit}: ", end="")
            for next_digit, prob in non_zero_probs:
                print(f"→{next_digit}({prob:.1%}) ", end="")
            print()
            
            # 验证概率和是否为1
            prob_sum = np.sum(model_probs)
            if abs(prob_sum - 1.0) > 0.01:
                print(f"❌ 数字{current_digit}的概率和不为1: {prob_sum:.3f}")
                return False
    
    print("\n🎯 预测功能测试")
    print("-" * 40)
    
    last_period = test_data[-1]  # [5, 1, 6, 4, 8]
    print(f"上期数据: {last_period}")
    
    print("\n各位置预测结果:")
    for pos in range(5):
        last_digit = last_period[pos]
        prediction = model._digit_transition_predict(pos)
        
        print(f"位置{pos}(上期数字{last_digit}): ", end="")
        
        # 显示非零概率
        non_zero = [(d, p) for d, p in enumerate(prediction) if p > 0]
        if len(non_zero) <= 5:  # 如果非零概率不多，显示全部
            for digit, prob in non_zero:
                print(f"{digit}({prob:.1%}) ", end="")
        else:  # 如果太多，只显示最高的几个
            sorted_probs = sorted(enumerate(prediction), key=lambda x: x[1], reverse=True)
            for digit, prob in sorted_probs[:3]:
                if prob > 0:
                    print(f"{digit}({prob:.1%}) ", end="")
            print("...")
        print()
        
        # 验证概率和
        prob_sum = np.sum(prediction)
        if abs(prob_sum - 1.0) > 0.01:
            print(f"❌ 位置{pos}概率和不为1: {prob_sum:.3f}")
            return False
    
    print("\n✅ 数字转移预测逻辑测试通过！")
    
    # 详细分析一个具体例子
    print("\n🔍 详细分析示例")
    print("-" * 40)
    
    pos = 0
    last_digit = last_period[pos]  # 位置0上期数字是5
    print(f"分析位置{pos}，上期数字{last_digit}")
    
    # 查看历史上数字5在位置0后面跟什么数字
    print("历史上数字5在位置0的转移记录:")
    for i in range(len(test_data) - 1):
        if test_data[i][pos] == last_digit:
            next_digit = test_data[i + 1][pos]
            print(f"  第{i+1}期位置{pos}数字{last_digit} → 第{i+2}期位置{pos}数字{next_digit}")
    
    # 显示模型的预测概率
    prediction = model._digit_transition_predict(pos)
    print(f"模型预测概率分布:")
    for d in range(10):
        if prediction[d] > 0:
            print(f"  数字{d}: {prediction[d]:.1%}")
    
    return True

if __name__ == "__main__":
    try:
        success = test_digit_transition_logic()
        if success:
            print("\n🎉 所有测试通过！数字转移预测逻辑正确！")
        else:
            print("\n❌ 测试失败！")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
