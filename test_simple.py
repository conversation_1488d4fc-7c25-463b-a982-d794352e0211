#!/usr/bin/env python
# -*- coding: utf-8 -*-

import numpy as np
import os

# 设置 __file__ 变量
__file__ = os.path.abspath('all - 副本.py')

# 读取副本文件内容
with open('all - 副本.py', 'r', encoding='utf-8') as f:
    content = f.read()

print('文件读取成功')

# 编译测试
compile(content, 'all - 副本.py', 'exec')
print('✓ 文件语法检查通过')

# 执行文件内容
exec_globals = {'__file__': __file__}
exec(content, exec_globals)

# 获取关键类和配置
DEFAULT_CONFIG = exec_globals['DEFAULT_CONFIG']
MFTNModel = exec_globals['MFTNModel']

print('✓ 成功导入关键类')

# 检查默认配置
print('\n默认配置:')
for key, value in DEFAULT_CONFIG.items():
    if 'weight' in key:
        print(f'  {key}: {value}')

# 检查权重总和
weight_sum = (DEFAULT_CONFIG['co_weight'] + 
              DEFAULT_CONFIG['digit_transition_weight'] + 
              DEFAULT_CONFIG['sum_transition_weight'] + 
              DEFAULT_CONFIG['digit_exclusion_weight'])
print(f'\n权重总和: {weight_sum}')

if abs(weight_sum - 1.0) < 0.001:
    print('✓ 权重总和正确')
else:
    print('✗ 权重总和不正确')

# 测试模型初始化
model = MFTNModel()
print('✓ 模型初始化成功')

# 检查模型参数
model_weights = {k: v for k, v in model.params.items() if 'weight' in k}
print('\n模型权重参数:')
for key, value in model_weights.items():
    print(f'  {key}: {value}')

# 检查模型是否有数字排除矩阵属性
if hasattr(model, 'digit_exclusion_matrix'):
    print('✓ 模型具有数字排除矩阵属性')
else:
    print('✗ 模型缺少数字排除矩阵属性')

# 检查是否有构建数字排除矩阵的方法
if hasattr(model, '_build_digit_exclusion_matrix'):
    print('✓ 模型具有构建数字排除矩阵的方法')
else:
    print('✗ 模型缺少构建数字排除矩阵的方法')

# 检查是否有数字排除预测方法
if hasattr(model, '_digit_exclusion_predict'):
    print('✓ 模型具有数字排除预测方法')
else:
    print('✗ 模型缺少数字排除预测方法')

print('\n=== 基本测试通过 ===')
