#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试参数设置对话框功能
"""

import tkinter as tk
from tkinter import messagebox
import json
import os

def test_complete_params_dialog():
    """完整测试参数设置对话框功能"""
    print("=== 完整测试参数设置对话框功能 ===")
    
    try:
        from all import DEFAULT_CONFIG, load_config, save_config, CONFIG_FILE
        
        # 1. 验证配置完整性
        print("1. 验证配置完整性...")
        config = load_config()
        required_params = [
            'alpha', 'lambda', 'co_weight', 'digit_transition_weight', 
            'digit_exclusion_weight', 'hot_threshold', 'cold_threshold', 
            'window', 'periodicity'
        ]
        
        all_present = True
        for param in required_params:
            if param in config:
                print(f"  ✓ {param}: {config[param]}")
            else:
                print(f"  ✗ {param}: 缺失")
                all_present = False
        
        if not all_present:
            print("  配置不完整，无法继续测试")
            return False
        
        # 2. 验证权重配置
        print("\n2. 验证权重配置...")
        weights = ['co_weight', 'digit_transition_weight', 'digit_exclusion_weight']
        weight_values = [config[w] for w in weights]
        weight_sum = sum(weight_values)
        
        print(f"  权重值: {dict(zip(weights, weight_values))}")
        print(f"  权重之和: {weight_sum}")
        
        if abs(weight_sum - 1.0) <= 0.01:
            print("  ✓ 权重配置正确")
        else:
            print("  ✗ 权重配置不正确")
            return False
        
        # 3. 模拟参数对话框逻辑
        print("\n3. 模拟参数对话框逻辑...")
        
        # 模拟应用类
        class MockApp:
            def __init__(self):
                self.config = load_config()
                self.default_params = {k: v for k, v in self.config.items() if k != 'last_excel_file'}
            
            def get_current_params(self):
                return self.default_params.copy()
        
        mock_app = MockApp()
        current_params = mock_app.get_current_params()
        
        # 参数标签
        param_labels = {
            'alpha': '平滑因子 (alpha):',
            'lambda': '冷号衰减系数 (lambda):',
            'co_weight': '协同预测权重:',
            'digit_transition_weight': '数字转移权重:',
            'digit_exclusion_weight': '数字排除权重:',
            'hot_threshold': '热号阈值:',
            'cold_threshold': '冷号阈值:',
            'window': '窗口大小:',
            'periodicity': '周期特征期数:'
        }
        
        # 验证所有参数都能获取到值
        param_entries = {}
        for param, label_text in param_labels.items():
            if param not in current_params:
                print(f"  ✗ 参数 {param} 不存在")
                return False
            
            # 模拟创建变量
            if param in ['window', 'periodicity']:
                var_value = int(current_params[param])
                var_type = "IntVar"
            else:
                var_value = float(current_params[param])
                var_type = "DoubleVar"
            
            param_entries[param] = var_value
            print(f"  ✓ {param} ({var_type}): {var_value}")
        
        # 4. 模拟参数验证逻辑
        print("\n4. 模拟参数验证逻辑...")
        
        # 测试正确的权重
        test_weights = [0.4, 0.3, 0.3]
        weight_sum = sum(test_weights)
        if abs(weight_sum - 1.0) <= 0.01:
            print(f"  ✓ 正确权重验证通过: {test_weights} (和={weight_sum})")
        else:
            print(f"  ✗ 正确权重验证失败: {test_weights} (和={weight_sum})")
            return False
        
        # 测试错误的权重
        test_weights = [0.5, 0.4, 0.3]
        weight_sum = sum(test_weights)
        if abs(weight_sum - 1.0) > 0.01:
            print(f"  ✓ 错误权重正确识别: {test_weights} (和={weight_sum})")
        else:
            print(f"  ✗ 错误权重未被识别: {test_weights} (和={weight_sum})")
        
        # 5. 模拟参数保存逻辑
        print("\n5. 模拟参数保存逻辑...")
        
        # 创建测试参数
        test_params = {
            'alpha': 2.0,
            'lambda': 0.1,
            'co_weight': 0.4,
            'digit_transition_weight': 0.3,
            'digit_exclusion_weight': 0.3,
            'hot_threshold': 1.5,
            'cold_threshold': 7.0,
            'window': 30,
            'periodicity': 14
        }
        
        # 保留文件路径
        if 'last_excel_file' in config:
            test_params['last_excel_file'] = config['last_excel_file']
        
        # 尝试保存
        if save_config(test_params):
            print("  ✓ 参数保存测试成功")
        else:
            print("  ✗ 参数保存测试失败")
            return False
        
        # 验证保存的参数
        saved_config = load_config()
        for param, expected_value in test_params.items():
            if param in saved_config and saved_config[param] == expected_value:
                print(f"    ✓ {param}: {saved_config[param]}")
            else:
                print(f"    ✗ {param}: 期望 {expected_value}, 实际 {saved_config.get(param, '缺失')}")
                return False
        
        print("\n🎉 参数设置对话框功能完整测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_integration():
    """测试GUI集成（如果可能的话）"""
    print("\n=== 测试GUI集成 ===")
    
    try:
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 模拟完整的参数对话框创建过程
        dialog = tk.Toplevel(root)
        dialog.title("模型参数设置")
        dialog.geometry("500x600")
        dialog.withdraw()  # 隐藏对话框
        
        from all import load_config
        
        # 获取当前参数
        config = load_config()
        current_params = {k: v for k, v in config.items() if k != 'last_excel_file'}
        
        # 创建参数输入框
        param_entries = {}
        param_labels = {
            'alpha': '平滑因子 (alpha):',
            'lambda': '冷号衰减系数 (lambda):',
            'co_weight': '协同预测权重:',
            'digit_transition_weight': '数字转移权重:',
            'digit_exclusion_weight': '数字排除权重:',
            'hot_threshold': '热号阈值:',
            'cold_threshold': '冷号阈值:',
            'window': '窗口大小:',
            'periodicity': '周期特征期数:'
        }
        
        frame = tk.Frame(dialog, padx=20, pady=10)
        frame.pack(fill=tk.BOTH, expand=True)
        
        success_count = 0
        for i, (param, label_text) in enumerate(param_labels.items()):
            try:
                # 创建标签
                label = tk.Label(frame, text=label_text, font=("SimHei", 10))
                label.grid(row=i, column=0, sticky=tk.W, pady=5)
                
                # 创建输入框
                if param in ['window', 'periodicity']:
                    var = tk.IntVar(value=int(current_params[param]))
                else:
                    var = tk.DoubleVar(value=current_params[param])
                entry = tk.Entry(frame, textvariable=var, width=10)
                entry.grid(row=i, column=1, sticky=tk.W, pady=5)
                
                param_entries[param] = var
                success_count += 1
                
                # 测试获取值
                test_value = var.get()
                expected_value = current_params[param]
                if abs(test_value - expected_value) < 0.0001:
                    print(f"  ✓ {param}: {test_value}")
                else:
                    print(f"  ✗ {param}: 期望 {expected_value}, 实际 {test_value}")
                    success_count -= 1
                
            except Exception as e:
                print(f"  ✗ 创建 {param} 失败: {e}")
        
        # 清理
        dialog.destroy()
        root.destroy()
        
        print(f"\nGUI集成测试结果: {success_count}/{len(param_labels)} 个参数成功")
        return success_count == len(param_labels)
        
    except Exception as e:
        print(f"  ✗ GUI集成测试失败: {e}")
        return False

def main():
    """主函数"""
    print("开始最终测试参数设置对话框功能...")
    print("="*60)
    
    # 完整功能测试
    complete_test = test_complete_params_dialog()
    
    # GUI集成测试
    gui_test = test_gui_integration()
    
    print("\n" + "="*60)
    if complete_test and gui_test:
        print("🎉 参数设置对话框功能完全正常！")
        print("\n测试结果:")
        print("✓ 配置文件完整性验证通过")
        print("✓ 权重配置正确")
        print("✓ 参数获取逻辑正常")
        print("✓ 参数验证逻辑正常")
        print("✓ 参数保存逻辑正常")
        print("✓ GUI组件创建正常")
        print("✓ 参数值获取正常")
        print("\n现在可以正常使用参数设置功能了！")
    else:
        print("❌ 参数设置对话框仍有问题")
    
    return complete_test and gui_test

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
