#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复配置文件，清理旧参数并确保新参数存在
"""

import json
import os

def fix_config_file():
    """修复配置文件"""
    print("=== 修复配置文件 ===")
    
    try:
        from all import DEFAULT_CONFIG, CONFIG_FILE, save_config
        
        print(f"配置文件路径: {CONFIG_FILE}")
        
        # 1. 读取当前配置文件
        current_config = {}
        if os.path.exists(CONFIG_FILE):
            try:
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    current_config = json.load(f)
                print("✓ 成功读取当前配置文件")
            except Exception as e:
                print(f"✗ 读取配置文件失败: {e}")
        else:
            print("配置文件不存在，将创建新的配置文件")
        
        # 2. 创建新的干净配置
        print("\n创建新的干净配置...")
        new_config = DEFAULT_CONFIG.copy()
        
        # 保留有用的旧配置值（如果存在且合理的话）
        preserved_params = {
            'alpha': (0.01, 10.0),           # 平滑因子范围
            'lambda': (0.01, 50.0),          # 冷号衰减系数范围
            'co_weight': (0.0, 1.0),         # 权重范围
            'digit_transition_weight': (0.0, 1.0),  # 权重范围
            'hot_threshold': (0.5, 5.0),     # 热号阈值范围
            'cold_threshold': (5.0, 50.0),   # 冷号阈值范围
            'window': (10, 100),             # 窗口大小范围
            'periodicity': (5, 30),          # 周期特征期数范围
        }

        # 处理数值参数
        for param, (min_val, max_val) in preserved_params.items():
            if param in current_config:
                old_value = current_config[param]
                if isinstance(old_value, (int, float)) and min_val <= old_value <= max_val:
                    # 数值参数在合理范围内则保留
                    new_config[param] = old_value
                    print(f"  保留 {param}: {old_value}")
                else:
                    print(f"  重置 {param}: {old_value} -> {new_config[param]} (超出范围)")
            else:
                print(f"  使用默认 {param}: {new_config[param]}")

        # 处理文件路径参数
        if 'last_excel_file' in current_config:
            new_config['last_excel_file'] = current_config['last_excel_file']
            print(f"  保留 last_excel_file: {current_config['last_excel_file']}")
        
        # 3. 确保权重之和为1
        print("\n调整权重参数...")
        weights = ['co_weight', 'digit_transition_weight', 'digit_exclusion_weight']
        current_weights = [new_config.get(w, 0) for w in weights]
        weight_sum = sum(current_weights)
        
        if abs(weight_sum - 1.0) > 0.01:
            print(f"  权重之和不正确: {weight_sum}")
            # 重置为默认权重
            new_config['co_weight'] = 0.4
            new_config['digit_transition_weight'] = 0.3
            new_config['digit_exclusion_weight'] = 0.3
            print("  重置为默认权重: co_weight=0.4, digit_transition_weight=0.3, digit_exclusion_weight=0.3")
        else:
            print(f"  权重之和正确: {weight_sum}")
        
        # 4. 保存新配置
        print("\n保存新配置...")
        if save_config(new_config):
            print("✓ 新配置已保存")
        else:
            print("✗ 保存新配置失败")
            return False
        
        # 5. 验证新配置
        print("\n验证新配置...")
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                saved_config = json.load(f)
            
            print("新配置内容:")
            for param, value in saved_config.items():
                print(f"  {param}: {value}")
            
            # 检查是否包含所有必需参数
            required_params = set(DEFAULT_CONFIG.keys())
            saved_params = set(saved_config.keys()) - {'last_excel_file'}
            
            if required_params.issubset(saved_params):
                print("✓ 所有必需参数都存在")
                return True
            else:
                missing = required_params - saved_params
                print(f"✗ 缺少参数: {missing}")
                return False
                
        except Exception as e:
            print(f"✗ 验证新配置失败: {e}")
            return False
        
    except Exception as e:
        print(f"✗ 修复配置文件失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始修复配置文件...")
    print("="*60)
    
    success = fix_config_file()
    
    print("\n" + "="*60)
    if success:
        print("🎉 配置文件修复完成！")
        print("\n修复内容:")
        print("✓ 清理了所有旧的无用参数")
        print("✓ 添加了缺失的新参数")
        print("✓ 保留了合理范围内的旧参数值")
        print("✓ 确保权重参数之和为1.0")
        print("✓ 保留了文件路径设置")
        print("\n现在参数设置对话框应该可以正常显示所有参数了！")
    else:
        print("❌ 配置文件修复失败")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
