import pandas as pd
import numpy as np
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import threading
import matplotlib
import random
import json
import os
import sys
import time
matplotlib.use("TkAgg")

# 定义配置文件路径，可根据实际情况修改
CONFIG_FILE = os.path.join(os.path.dirname(__file__), 'config.json')
INIT_FILE = "init.json"  # 添加初始化配置文件路径

# 默认配置
DEFAULT_CONFIG = {
    'alpha': 2.0,
    'lambda': 0.1,
    'co_weight': 0.25,
    'digit_transition_weight': 0.25,  # 数字转移权重
    'sum_transition_weight': 0.25,    # 新增：每期数字和转移权重
    'digit_exclusion_weight': 0.25,   # 新增：数字排除权重
    'hot_threshold': 1.5,
    'cold_threshold': 7.0,
    'selection_count': 8,            # 每个位置选择的数字数量
    'window': 30,
    'periodicity': 14
}

def resource_path(relative_path):
    """获取资源文件的绝对路径"""
    try:
        # PyInstaller创建临时文件夹，将路径存储在_MEIPASS中
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)

def load_config():
    """加载配置文件"""
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        # 如果配置文件不存在，创建默认配置
        default_config = DEFAULT_CONFIG.copy()
        save_config(default_config)
        return default_config
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return DEFAULT_CONFIG.copy()

def save_config(config):
    """保存配置到文件"""
    try:
        print(f"尝试保存配置到文件: {CONFIG_FILE}")
        # 确保配置文件所在目录存在
        os.makedirs(os.path.dirname(CONFIG_FILE), exist_ok=True)
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"保存配置文件失败: {e}")
        return False

def load_init_config():
    """加载初始化配置文件"""
    try:
        if os.path.exists(INIT_FILE):
            with open(INIT_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return None
    except Exception as e:
        print(f"加载初始化配置文件失败: {e}")
        return None

def apply_init_config():
    """应用初始化配置"""
    init_config = load_init_config()
    if init_config is None:
        return False, "初始化配置文件不存在或格式错误"
    
    try:
        # 验证初始化配置中是否包含所有必要的参数
        required_params = ['alpha', 'lambda', 'co_weight', 'digit_transition_weight',
                         'sum_transition_weight', 'digit_exclusion_weight', 'hot_threshold', 'cold_threshold']
        if not all(param in init_config for param in required_params):
            return False, "初始化配置文件缺少必要的参数"

        # 验证权重之和是否为1
        weights_sum = init_config['co_weight'] + init_config['digit_transition_weight'] + init_config['sum_transition_weight'] + init_config['digit_exclusion_weight']
        if abs(weights_sum - 1.0) > 0.001:
            return False, "权重参数之和必须等于1"
            
        # 保存到config.json
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(init_config, f, indent=4, ensure_ascii=False)
        return True, "初始化参数已成功应用"
    except Exception as e:
        return False, f"应用初始化参数失败: {str(e)}"

class MFTNModel:
    def __init__(self, params=None):
        # 使用配置文件中的参数或用户提供的参数
        self.params = params or load_config()
        # 设置默认窗口参数
        self.params['window'] = int(round(self.params.get('window', 30)))
        # 设置默认周期性参数
        self.params['periodicity'] = int(round(self.params.get('periodicity', 14)))
        # 设置默认冷启动乘数参数
        self.params.setdefault('cold_multiplier', 1.0)
        # 设置默认选择数量参数
        self.params['selection_count'] = int(round(self.params.get('selection_count', 2)))
        # 设置数字转移权重参数
        self.params.setdefault('digit_transition_weight', 0.25)  # 数字转移权重
        # 设置数字和转移权重参数
        self.params.setdefault('sum_transition_weight', 0.25)   # 新增：数字和转移权重
        # 设置数字排除权重参数
        self.params.setdefault('digit_exclusion_weight', 0.25)   # 新增：数字排除权重
        # 设置随机种子确保结果可复现
        self.seed = self.params.get('seed', 42)
        import random
        random.seed(self.seed)
        import numpy as np
        np.random.seed(self.seed)
        self.history = None
        self.heat_index = None
        self.miss_counts = None
        self.position_triplets = None
        self.corr_matrix = None
        self.predictions_history = []  # 初始化预测历史记录
        self.digit_transition_probs = None  # 存储数字转移概率矩阵
        self.sum_transition_probs = None    # 存储数字和转移概率矩阵
        self.digit_exclusion_matrix = None  # 存储数字排除矩阵
        
    def fit(self, history_data):
        """训练模型"""
        self.history = np.array(history_data)
        self._initialize_thermal_state()
        self._build_correlation_matrix()
        self._find_position_triplets()
        self._build_digit_transition_matrix()  # 构建数字转移矩阵
        self._build_sum_transition_matrix()    # 新增：构建数字和转移矩阵
        self._build_digit_exclusion_matrix()   # 新增：构建数字排除矩阵
    
    def update_actual(self, actual_results):
        """更新实际结果用于准确率评估"""
        self.current_actual = actual_results
        # 可以在这里添加自动重新训练逻辑
        # self.fit(self.history)
        
    def predict_next(self, is_predict=False):
        """预测下一期号码，is_predict=True时为正式预测，False为回测/优化"""
        if self.history is None:
            raise ValueError("模型未训练，请先调用fit方法")
        predictions = {}
        for pos in range(5):
            co_prediction = self._co_predict(pos)
            digit_transition = self._digit_transition_predict(pos)  # 数字转移预测
            sum_transition = self._sum_transition_predict(pos)      # 新增：数字和转移预测
            digit_exclusion = self._digit_exclusion_predict(pos)    # 新增：数字排除预测

            # 调整权重总和，确保所有权重之和为1
            base_weight_sum = (self.params['co_weight'] +
                              self.params['digit_transition_weight'] +
                              self.params['sum_transition_weight'] +
                              self.params['digit_exclusion_weight'])

            weights = (self.params['co_weight'] / base_weight_sum) * co_prediction + \
                     (self.params['digit_transition_weight'] / base_weight_sum) * digit_transition + \
                     (self.params['sum_transition_weight'] / base_weight_sum) * sum_transition + \
                     (self.params['digit_exclusion_weight'] / base_weight_sum) * digit_exclusion  # 新增：数字排除权重

            if hasattr(self, 'predictions_history') and len(self.predictions_history) > 10:
                acc_weights = np.array([self._get_accuracy_weight(t) for t in ['co', 'digit', 'sum', 'exclusion']])
                acc_weights = acc_weights / np.sum(acc_weights)
                weights = acc_weights[0] * co_prediction + \
                          acc_weights[1] * digit_transition + \
                          acc_weights[2] * sum_transition + \
                          acc_weights[3] * digit_exclusion  # 新增：数字排除权重

            predictions[pos] = self._quantum_selection(weights, pos, is_predict=is_predict)
        return predictions
    
    # 内部实现方法
    def _initialize_thermal_state(self):
        """初始化热力状态"""
        n_periods, n_positions = self.history.shape
        self.heat_index = np.ones((n_positions, 10))
        self.miss_counts = np.zeros((n_positions, 10))
        
        # 计算初始遗漏状态
        for pos in range(n_positions):
            for num in range(10):
                mask = self.history[:, pos] == num
                if any(mask):
                    last_occur = np.where(mask)[0][-1]
                    self.miss_counts[pos, num] = n_periods - last_occur - 1
                else:
                    self.miss_counts[pos, num] = n_periods
    
    def _build_correlation_matrix(self):
        """构建位置相关系数矩阵"""
        n_positions = self.history.shape[1]
        self.corr_matrix = np.zeros((n_positions, n_positions))
        
        for i in range(n_positions):
            for j in range(n_positions):
                if i != j:
                    cov = np.cov(self.history[:, i], self.history[:, j])[0, 1]
                    var_i = np.var(self.history[:, i])
                    var_j = np.var(self.history[:, j])
                    self.corr_matrix[i, j] = cov / (np.sqrt(var_i) * np.sqrt(var_j))
    
    def _find_position_triplets(self):
        """为每球五置寻找最优三联位置"""
        self.position_triplets = {}
        n_positions = self.history.shape[1]
        
        for target_pos in range(n_positions):
            max_corr = 0
            best_triplet = None
            
            # 从其他位置中选择三个
            other_positions = [p for p in range(n_positions) if p != target_pos]
            for i in range(len(other_positions)):
                for j in range(i+1, len(other_positions)):
                    for k in range(j+1, len(other_positions)):
                        triplet = [other_positions[i], other_positions[j], other_positions[k]]
                        corr_product = np.prod([abs(self.corr_matrix[target_pos, p]) for p in triplet])
                        
                        if corr_product > max_corr:
                            max_corr = corr_product
                            best_triplet = triplet
            
            self.position_triplets[target_pos] = best_triplet
    

    
    def _get_accuracy_weight(self, pred_type):
        """计算不同预测类型的准确率权重，支持多指标评估"""
        if len(self.predictions_history) < 10:
            return 1.0  # 历史数据不足时返回默认权重
        
        # 多指标评估：准确率、精确率、召回率
        metrics = {}
        total_hits = 0
        total_predicted = 0
        total_actual = 0
        total_correct = 0
        
        for record in self.predictions_history[-10:]:  # 最近10次预测
            predicted = record.get('predicted', [])
            actual = record.get('actual', [])
            if not isinstance(predicted, list) or not isinstance(actual, list):
                continue
            
            # 计算命中数量
            hits = len(set(predicted) & set(actual))
            total_hits += hits
            total_predicted += len(predicted)
            total_actual += len(actual)
            
            # 计算全中次数
            if set(predicted) == set(actual) and len(predicted) > 0:
                total_correct += 1
        
        # 计算各指标（添加平滑项）
        accuracy = (total_correct + 1e-6) / (10 + 1e-6)  # 全中准确率
        precision = (total_hits + 1e-6) / (total_predicted + 1e-6) if total_predicted > 0 else 0
        recall = (total_hits + 1e-6) / (total_actual + 1e-6) if total_actual > 0 else 0
        f1_score = 2 * (precision * recall) / (precision + recall + 1e-6) if (precision + recall) > 0 else 0
        
        # 综合权重：F1分数（0.6）+ 全中准确率（0.4）
        combined_weight = 0.4 * f1_score + 0.6 * accuracy
        return combined_weight if combined_weight > 0 else 1.0
    
    def _generate_confusion_matrix(self):
        """生成混淆矩阵分析预测偏差模式"""
        if len(self.predictions_history) < 20:
            return None
        
        # 初始化10x10混淆矩阵（预测->实际）
        cm = np.zeros((10, 10), dtype=int)
        
        for record in self.predictions_history:
            predicted = record.get('predicted', [])
            actual = record.get('actual', [])
            for p in predicted:
                for a in actual:
                    cm[p][a] += 1
        
        # 归一化混淆矩阵
        row_sums = cm.sum(axis=1, keepdims=True)
        return cm / row_sums if np.any(row_sums) else cm
    
    def _co_predict(self, position):
        """位置协同预测"""
        if position not in self.position_triplets or not self.position_triplets[position]:
            return np.ones(10) / 10
        
        triplet = self.position_triplets[position]
        joint_probs = np.zeros(10)
        
        # 简化的联合概率估计
        for d in range(10):
            mask = self.history[:, position] == d
            if np.any(mask):
                triplet_values = self.history[mask][:, triplet]
                unique, counts = np.unique(triplet_values, axis=0, return_counts=True)
                max_count = np.max(counts) if len(counts) > 0 else 0
                joint_probs[d] = max_count / len(mask)
        
        return joint_probs / np.sum(joint_probs)
        
    def _build_digit_transition_matrix(self):
        """构建数字转移概率矩阵"""
        if self.history is None:
            return

        n_periods, n_positions = self.history.shape
        # 初始化10x10的转移概率矩阵，每个位置一个
        self.digit_transition_probs = np.zeros((n_positions, 10, 10))

        for pos in range(n_positions):
            # 对于每个位置，统计当前数字到下一期数字的转移频率
            for i in range(n_periods - 1):
                current_digit = self.history[i, pos]
                next_digit = self.history[i + 1, pos]
                self.digit_transition_probs[pos, current_digit, next_digit] += 1

            # 归一化得到概率
            for d in range(10):
                row_sum = np.sum(self.digit_transition_probs[pos, d])
                if row_sum > 0:
                    self.digit_transition_probs[pos, d] /= row_sum
                else:
                    # 如果某个数字从未出现，使用均匀分布
                    self.digit_transition_probs[pos, d] = np.ones(10) / 10

    def _build_digit_exclusion_matrix(self):
        """构建数字排除矩阵"""
        if self.history is None:
            return

        n_periods, n_positions = self.history.shape
        # 初始化排除统计矩阵：[数字][下期位置] = 出现次数
        digit_position_counts = np.zeros((10, n_positions))

        # 统计每个数字在下期各位置的出现频率
        for i in range(n_periods - 1):
            current_period = self.history[i]
            next_period = self.history[i + 1]

            # 对本期出现的每个数字，统计它们在下期各位置的出现情况
            for current_digit in current_period:
                for next_pos in range(n_positions):
                    next_digit = next_period[next_pos]
                    if current_digit == next_digit:
                        digit_position_counts[current_digit, next_pos] += 1

        # 计算每个数字的排除权重：找出每个数字最不可能出现的两个位置
        self.digit_exclusion_matrix = np.ones((10, n_positions))  # [数字][位置] = 权重

        for digit in range(10):
            # 获取该数字在各位置的出现频率
            frequencies = digit_position_counts[digit]
            total_count = np.sum(frequencies)

            if total_count >= 3:  # 至少需要3次出现才进行排除计算
                # 计算概率分布
                probabilities = frequencies / total_count
                # 找出概率最小的两个位置
                sorted_indices = np.argsort(probabilities)
                least_likely_positions = sorted_indices[:2]

                # 只有当最小概率明显小于平均概率时才进行排除
                avg_prob = 1.0 / n_positions  # 平均概率 = 20%
                min_prob = probabilities[least_likely_positions[0]]

                if min_prob < avg_prob * 0.7:  # 如果最小概率小于平均概率的70% (即14%)
                    # 对最不可能的两个位置设置排除权重（减小权重）
                    for pos in least_likely_positions:
                        if probabilities[pos] < avg_prob * 0.8:  # 只排除明显偏低的位置 (即16%)
                            self.digit_exclusion_matrix[digit, pos] = 0.3  # 减小到30%
            elif total_count == 0:
                # 如果该数字从未出现过，则对随机两个位置进行轻微排除
                random_positions = np.random.choice(n_positions, size=2, replace=False)
                for pos in random_positions:
                    self.digit_exclusion_matrix[digit, pos] = 0.7  # 轻微减小到70%
        
    def _digit_transition_predict(self, position):
        """基于数字转移概率的预测"""
        if self.digit_transition_probs is None or len(self.history) < 2:
            return np.ones(10) / 10  # 数据不足时返回均匀分布

        # 获取上一期的数字
        last_digit = self.history[-1, position]
        # 返回从该数字出发的转移概率
        return self.digit_transition_probs[position, last_digit]

    def _build_sum_transition_matrix(self):
        """构建数字和转移概率矩阵"""
        if self.history is None or len(self.history) < 2:
            return

        # 初始化转移概率矩阵：[位置, 和值] -> 数字概率
        # 和值范围：0-45 (5个数字，每个0-9)
        self.sum_transition_probs = np.ones((5, 46, 10)) * 0.1  # 5个位置，46个可能的和值，10个数字

        # 统计转移频次
        transition_counts = np.ones((5, 46, 10))  # 加1平滑

        for i in range(1, len(self.history)):
            prev_sum = np.sum(self.history[i-1])  # 上一期的数字和
            prev_sum = min(45, max(0, prev_sum))  # 确保在有效范围内

            for pos in range(5):
                current_digit = self.history[i, pos]  # 当前期该位置的数字
                transition_counts[pos, prev_sum, current_digit] += 1

        # 计算转移概率
        for pos in range(5):
            for sum_val in range(46):
                total_count = np.sum(transition_counts[pos, sum_val])
                if total_count > 0:
                    self.sum_transition_probs[pos, sum_val] = transition_counts[pos, sum_val] / total_count
                else:
                    self.sum_transition_probs[pos, sum_val] = np.ones(10) / 10  # 均匀分布

    def _sum_transition_predict(self, position):
        """基于数字和转移概率的预测"""
        if self.sum_transition_probs is None or len(self.history) < 2:
            return np.ones(10) / 10  # 数据不足时返回均匀分布

        # 获取上一期的数字和
        last_sum = np.sum(self.history[-1])
        # 确保和值在有效范围内
        last_sum = min(45, max(0, last_sum))
        # 返回从该和值出发到当前位置的转移概率
        return self.sum_transition_probs[position, last_sum]

    def _build_digit_exclusion_matrix(self):
        """构建数字排除矩阵"""
        if self.history is None:
            return

        n_periods, n_positions = self.history.shape
        # 初始化排除统计矩阵：[数字][下期位置] = 出现次数
        digit_position_counts = np.zeros((10, n_positions))

        # 统计每个数字在下期各位置的出现频率
        for i in range(n_periods - 1):
            current_period = self.history[i]
            next_period = self.history[i + 1]

            # 对本期出现的每个数字，统计它们在下期各位置的出现情况
            for current_digit in current_period:
                for next_pos in range(n_positions):
                    next_digit = next_period[next_pos]
                    if current_digit == next_digit:
                        digit_position_counts[current_digit, next_pos] += 1

        # 计算每个数字的排除权重：找出每个数字最不可能出现的两个位置
        self.digit_exclusion_matrix = np.ones((10, n_positions))  # [数字][位置] = 权重

        for digit in range(10):
            # 获取该数字在各位置的出现频率
            frequencies = digit_position_counts[digit]
            total_count = np.sum(frequencies)

            if total_count >= 3:  # 至少需要3次出现才进行排除计算
                # 计算概率分布
                probabilities = frequencies / total_count
                # 找出概率最小的两个位置
                sorted_indices = np.argsort(probabilities)
                least_likely_positions = sorted_indices[:2]

                # 只有当最小概率明显小于平均概率时才进行排除
                avg_prob = 1.0 / n_positions  # 平均概率 = 20%
                min_prob = probabilities[least_likely_positions[0]]

                if min_prob < avg_prob * 0.7:  # 如果最小概率小于平均概率的70% (即14%)
                    # 对最不可能的两个位置设置排除权重（减小权重）
                    for pos in least_likely_positions:
                        if probabilities[pos] < avg_prob * 0.8:  # 只排除明显偏低的位置 (即16%)
                            self.digit_exclusion_matrix[digit, pos] = 0.3  # 减小到30%
            elif total_count == 0:
                # 如果该数字从未出现过，则对随机两个位置进行轻微排除
                random_positions = np.random.choice(n_positions, size=2, replace=False)
                for pos in random_positions:
                    self.digit_exclusion_matrix[digit, pos] = 0.7  # 轻微减小到70%

    def _digit_exclusion_predict(self, position):
        """基于数字排除权重的预测"""
        if self.digit_exclusion_matrix is None or len(self.history) < 2:
            return np.ones(10) / 10  # 数据不足时返回均匀分布

        # 获取上一期各位置的数字
        last_period = self.history[-1]

        # 初始化预测概率（均匀分布）
        prediction_probs = np.ones(10) / 10

        # 根据上一期出现的每个数字，调整它们在当前位置的权重
        for digit in last_period:
            # 获取该数字在当前位置的排除权重
            exclusion_weight = self.digit_exclusion_matrix[digit, position]
            # 调整该数字在当前位置的概率
            prediction_probs[digit] *= exclusion_weight

        # 归一化概率
        total_prob = np.sum(prediction_probs)
        if total_prob > 0:
            prediction_probs = prediction_probs / total_prob
        else:
            prediction_probs = np.ones(10) / 10

        return prediction_probs
    
    def _quantum_selection(self, weights, position, is_predict=False):
        """量子化选择系统，is_predict=True时为正式预测，False为回测/优化"""
        weights = weights / np.sum(weights)
        for d in range(10):
            if self.heat_index[position, d] > self.params['hot_threshold']:
                weights[d] *= self.params['hot_multiplier']
            elif self.miss_counts[position, d] > self.params['cold_threshold']:
                weights[d] *= self.params['cold_multiplier']
        cm = self._generate_confusion_matrix()
        if cm is not None:
            for d in range(10):
                confusion_factor = np.sum(cm[d]) - cm[d][d]
                weights[d] *= (1 - confusion_factor * 0.01)
        selection_count = int(round(self.params['selection_count']))
        exp_weights = np.exp(weights - np.max(weights))
        probabilities = exp_weights / np.sum(exp_weights)
        if is_predict:
            # 预测时，严格选取权重最高的N个号码
            sorted_indices = np.argsort(probabilities)[::-1]
            selected = sorted_indices[:selection_count]
            predicted = sorted(selected.tolist())
        else:
            # 回测/优化时，使用确定性选择，移除随机性
            sorted_indices = np.argsort(probabilities)[::-1]
            selected = sorted_indices[:selection_count]
            predicted = sorted(selected.tolist())
        if hasattr(self, 'current_actual'):
            self.predictions_history.append({
                'predicted': predicted,
                'actual': self.current_actual[position],
                'timestamp': time.time()
            })
            if len(self.predictions_history) > 100:
                self.predictions_history.pop(0)
        return predicted

class ParameterOptimizer:
    def __init__(self, data_file, target_hit_rate=0.8, max_iterations=1000, population_size=100, num_threads=4):
        self.data_file = data_file
        self.target_hit_rate = target_hit_rate
        self.max_iterations = max_iterations
        self.population_size = population_size
        self.num_threads = num_threads  # 线程数
        self.progress_callback = None
        self.optimization_running = True
        
        # 线程同步相关
        self.result_lock = threading.Lock()
        self.thread_results = []
        self.best_result = {'hit_rate': 0.0, 'params': None}
        self.best_hit_rate = 0.0  # 添加最佳命中率记录
        self.current_iteration = 0  # 当前迭代次数
        self.iteration_lock = threading.Lock()  # 迭代计数锁
        
        # 定义参数搜索范围
        self.param_ranges = {
            'alpha': (0.01, 50.0),         # 平滑因子
            'lambda': (0.01, 50.0),        # 冷号衰减系数
            'co_weight': (0.0, 1.0),       # 协同预测权重
            'digit_transition_weight': (0.0, 1.0), # 数字转移权重
            'sum_transition_weight': (0.0, 1.0),   # 数字和转移权重
            'digit_exclusion_weight': (0.0, 1.0),  # 数字排除权重
            'overall_weight': (0.99, 1.0), # 全中率权重（提高下限）
            'hot_threshold': (0.01, 50.0),  # 热号阈值
            'cold_threshold': (0.01, 50.0), # 冷号阈值
            'selection_count': (8, 9),     # 进一步缩小选择范围以提高精度
            'window': (30, 50),             # 窗口大小范围
            'periodicity': (5,20),         # 周期特征期数范围
            'hot_multiplier': (0.01, 50.0),  # 调整热号权重系数范围
            'cold_multiplier': (0.01, 50.0)  # 冷号权重系数（进一步扩大范围）
        }
        
        # 存储优化历史
        self.optimization_history = []
        self.best_params = None
        
        # 添加精英保留数量
        self.elite_size = 3
        
        # 添加交叉和变异概率
        # 初始化遗传算法参数（将在优化过程中动态调整）
        # 提高变异率以增强探索能力
        self.crossover_rate = 0.8
        self.mutation_rate = 0.5

    def optimize_thread(self, thread_id, evaluate_function, shared_population):
        """单个线程的优化过程"""
        local_best = {'hit_rate': 0.0, 'params': None}
        no_improvement_count = 0
        last_best_rate = 0
        
        print(f"线程 {thread_id} 开始运行")
        
        while True:
            try:
                # 检查是否达到最大迭代次数
                with self.iteration_lock:
                    if self.current_iteration >= self.max_iterations or not self.optimization_running:
                        print(f"线程 {thread_id} 退出：迭代完成或停止优化")
                        break
                    # 只有主线程（thread_id == 0）负责增加迭代计数
                    if thread_id == 0:
                        self.current_iteration += 1
                    current_iter = self.current_iteration
                
                # 从共享种群中获取当前种群的副本
                with self.result_lock:
                    if not shared_population:
                        print(f"线程 {thread_id} 警告：共享种群为空")
                        continue
                    population = shared_population.copy()
                
                print(f"线程 {thread_id} 迭代 {current_iter}：开始评估 {len(population)} 个个体")
                
                # 评估种群
                evaluated_population = []
                for i, params in enumerate(population):
                    if not self.optimization_running:
                        print(f"线程 {thread_id} 中断：优化已停止")
                        break
                    try:
                        hit_rate = evaluate_function(params)
                        evaluated_population.append((params, hit_rate))
                        
                        # 更新局部最佳结果
                        if hit_rate > local_best['hit_rate']:
                            local_best = {'hit_rate': hit_rate, 'params': params.copy()}
                            
                            # 更新全局最佳结果
                            with self.result_lock:
                                if hit_rate > self.best_hit_rate:
                                    print(f"线程 {thread_id} 发现更好结果：{hit_rate*100:.2f}%")
                                    self.best_hit_rate = hit_rate
                                    self.best_params = params.copy()
                                    self.optimization_history.append({
                                        'iteration': current_iter,
                                        'params': params.copy(),
                                        'hit_rate': hit_rate
                                    })
                    except Exception as e:
                        print(f"线程 {thread_id} 评估参数时发生错误: {e}")
                        continue
                
                if not self.optimization_running:
                    print(f"线程 {thread_id} 退出：优化已停止")
                    break
                
                if not evaluated_population:
                    print(f"线程 {thread_id} 警告：没有成功评估的个体")
                    continue
                
                # 排序并生成新种群
                evaluated_population.sort(key=lambda x: x[1], reverse=True)
                current_best_rate = evaluated_population[0][1]
                
                print(f"线程 {thread_id} 迭代 {current_iter}：当前最佳命中率 {current_best_rate*100:.2f}%")
                
                # 检查是否有改进
                # 检查是否有改进
                if current_best_rate > last_best_rate:
                    # 找到更好解时降低变异率以 exploitation
                    self.mutation_rate = max(0.05, self.mutation_rate * 0.85)
                    no_improvement_count = 0
                    last_best_rate = current_best_rate
                    print(f"线程 {thread_id} 发现改进，降低变异率至：{self.mutation_rate:.3f}")
                else:
                    no_improvement_count += 1
                    # 长时间无改进时大幅提高变异率以 exploration
                    if no_improvement_count > 20:
                        self.mutation_rate = min(0.5, self.mutation_rate * 1.5)
                        no_improvement_count = 0
                        print(f"线程 {thread_id} 无改进，提高变异率至：{self.mutation_rate:.3f}")
                    else:
                        # 轻微提高变异率
                        self.mutation_rate = min(0.5, self.mutation_rate * 1.05)
                
                # 生成新种群
                new_population = [p[0] for p in evaluated_population[:self.elite_size]]
                
                generation_attempts = 0
                max_generation_attempts = 200
                
                while len(new_population) < self.population_size and generation_attempts < max_generation_attempts:
                    try:
                        parent1 = self._tournament_selection(evaluated_population)
                        parent2 = self._tournament_selection(evaluated_population)
                        child = self._crossover(parent1, parent2)
                        child = self._mutate(child)
                        total_weight = child['co_weight'] + child['digit_transition_weight'] + child['sum_transition_weight'] + child['digit_exclusion_weight']
                        if abs(total_weight - 1.0) < 0.1:  # 放宽权重约束
                            new_population.append(child)
                    except Exception as e:
                        print(f"线程 {thread_id} 生成子代时发生错误: {e}")
                    generation_attempts += 1
                
                # 如果新种群仍然不够，补充随机个体
                while len(new_population) < self.population_size:
                    try:
                        random_params = self._generate_random_params()
                        new_population.append(random_params)
                    except Exception as e:
                        print(f"线程 {thread_id} 生成随机个体时发生错误: {e}")
                        break
                
                print(f"线程 {thread_id} 迭代 {current_iter}：生成了 {len(new_population)} 个新个体")
                
                # 更新共享种群
                with self.result_lock:
                    shared_population.clear()
                    shared_population.extend(new_population[:self.population_size])
                
                # 早停机制：连续30次迭代无改进则停止
                if no_improvement_count >= 30:
                    print(f"线程 {thread_id} 触发早停：连续30次迭代无改进")
                    break
                
                # 主线程负责更新进度，传递正确的迭代次数
                if thread_id == 0 and self.progress_callback:
                    # 传递当前迭代次数
                    try:
                        if not self.progress_callback(current_iter, self.max_iterations, current_best_rate, evaluated_population[0][0]):
                            print(f"线程 {thread_id} 退出：进度回调返回False")
                            break
                    except Exception as e:
                        print(f"线程 {thread_id} 进度回调发生错误: {e}")
                
                # 添加小延迟，避免过度占用CPU
                import time
                time.sleep(0.01)
                
            except Exception as e:
                print(f"优化线程 {thread_id} 发生未捕获错误: {e}")
                import traceback
                traceback.print_exc()
                continue
        
        print(f"线程 {thread_id} 结束运行")

    def optimize(self, evaluate_function):
        """使用单线程遗传算法优化参数，加入时间序列交叉验证和正则化"""
        print("开始优化过程...")
        self.optimization_running = True
        self.current_iteration = 0
        self.thread_results = []
        self.best_hit_rate = 0.0
        self.best_params = None
        self.best_validation_rate = 0.0  # 验证集最佳命中率
        self.no_improvement_count = 0  # 早停计数器
        self.mutation_rate = 0.3  # 初始变异率
        
        # 生成初始种群
        population = []
        print("生成初始种群...")
        
        # 随机生成初始种群
        population = []
        print("生成初始种群...")
        while len(population) < self.population_size:
            params = self._generate_random_params()
            population.append(params)
        
        # 批量评估初始种群
        print("评估初始种群...")
        for i, params in enumerate(population):
            # 评估新生成的参数（使用交叉验证）
            train_rate, val_rate = self._time_series_cv_evaluate(evaluate_function, params)
            combined_score = 0.7 * val_rate + 0.3 * train_rate  # 更重视验证集表现
            if combined_score > self.best_hit_rate:
                self.best_hit_rate = combined_score
                self.best_validation_rate = val_rate
                self.best_params = params.copy()
                print(f"初始种群发现更好结果({i+1}/{self.population_size})：训练{train_rate*100:.2f}%，验证{val_rate*100:.2f}%")
        
        print(f"初始种群生成完成，共 {len(population)} 个个体")
        
        # 主优化循环
        for iteration in range(1, self.max_iterations + 1):
            if not self.optimization_running:
                print("优化被停止")
                break
                
            print(f"开始第 {iteration} 次迭代")
            self.current_iteration = iteration
            
            # 评估种群
            evaluated_population = []
            for i, params in enumerate(population):
                if not self.optimization_running:
                    break
                try:
                    # 使用时间序列交叉验证评估参数
                    train_rate, val_rate = self._time_series_cv_evaluate(evaluate_function, params)
                    # 添加正则化项：惩罚极端权重值
                    weight_reg = self._weight_regularization(params)
                    # 综合评分：验证集表现(0.7) + 训练集表现(0.3) - 正则化惩罚
                    combined_score = 0.7 * val_rate + 0.3 * train_rate - weight_reg
                    evaluated_population.append((params, combined_score, train_rate, val_rate))
                    
                    # 更新最佳结果
                    if combined_score > self.best_hit_rate:
                        self.best_hit_rate = combined_score
                        self.best_validation_rate = val_rate
                        self.best_params = params.copy()
                        self.optimization_history.append({
                            'iteration': iteration,
                            'params': params.copy(),
                            'train_rate': train_rate,
                            'val_rate': val_rate,
                            'combined_score': combined_score
                        })
                        print(f"第 {iteration} 次迭代发现更好结果：训练{train_rate*100:.2f}%，验证{val_rate*100:.2f}%")
                        
                except Exception as e:
                    print(f"评估参数时发生错误: {e}")
                    continue
            
            if not evaluated_population:
                print("没有成功评估的个体，跳过此次迭代")
                continue
                
            # 排序
            evaluated_population.sort(key=lambda x: x[1], reverse=True)
            current_best_score = evaluated_population[0][1]
            current_best_train = evaluated_population[0][2]
            current_best_val = evaluated_population[0][3]
            current_best_params = evaluated_population[0][0]
            
            print(f"第 {iteration} 次迭代完成，当前最佳评分：{current_best_score:.4f} (训练{current_best_train*100:.2f}%，验证{current_best_val*100:.2f}%)")
            
            # 早停检查
            if current_best_score > self.best_hit_rate:
                self.no_improvement_count = 0
                # 找到更好结果，降低变异率
                self.mutation_rate = max(0.1, self.mutation_rate - 0.02)
                print(f"找到更好结果，降低变异率至 {self.mutation_rate:.2f}")
            else:
                self.no_improvement_count += 1
                print(f"连续无改进次数: {self.no_improvement_count}/30")
                # 连续无改进，提高变异率
                if self.no_improvement_count % 5 == 0:
                    self.mutation_rate = min(0.6, self.mutation_rate + 0.05)
                    print(f"连续无改进，提高变异率至 {self.mutation_rate:.2f}")
                if self.no_improvement_count >= 30:
                    print("连续30次迭代无改进，触发早停")
                    break
            
            # 更新进度
            if self.progress_callback:
                try:
                    if not self.progress_callback(iteration, self.max_iterations, current_best_val, current_best_params):
                        print("进度回调返回False，停止优化")
                        break
                except Exception as e:
                    print(f"进度回调发生错误: {e}")
            
            # 生成新种群
            new_population = [p[0] for p in evaluated_population[:self.elite_size]]
            
            generation_attempts = 0
            max_generation_attempts = 200  # 增加生成尝试次数
            while len(new_population) < self.population_size and generation_attempts < max_generation_attempts:
                try:
                    parent1 = self._tournament_selection(evaluated_population)
                    parent2 = self._tournament_selection(evaluated_population)
                    child = self._crossover(parent1, parent2)
                    child = self._mutate(child)
                    total_weight = child['co_weight'] + child['digit_transition_weight'] + child['sum_transition_weight'] + child['digit_exclusion_weight']
                    if abs(total_weight - 1.0) < 0.1:
                        new_population.append(child)
                except Exception as e:
                    print(f"生成子代时发生错误: {e}")
                generation_attempts += 1
            
            # 如果新种群不够，补充随机个体
            while len(new_population) < self.population_size:
                try:
                    random_params = self._generate_random_params()
                    new_population.append(random_params)
                except Exception as e:
                    print(f"生成随机个体时发生错误: {e}")
                    break
            
            population = new_population
            print(f"第 {iteration} 次迭代生成了 {len(population)} 个新个体")
        
        print(f"优化完成，最佳验证集命中率：{self.best_validation_rate*100:.2f}%")
        return self.best_params, self.best_validation_rate
        
    def _time_series_cv_evaluate(self, base_evaluate, params, n_splits=3):
        """时间序列交叉验证评估函数"""
        from sklearn.model_selection import TimeSeriesSplit
        
        # 获取完整历史数据
        if not hasattr(self, 'full_history_data'):
            # 假设我们可以通过某种方式获取完整历史数据
            # 这里需要根据实际情况调整获取数据的方式
            # 直接加载数据而不实例化GUI应用
            from pandas import read_excel
            data = read_excel(self.data_file, header=None).values
            self.full_history_data = data
            
        tscv = TimeSeriesSplit(n_splits=n_splits)
        train_scores = []
        val_scores = []
        
        for train_index, test_index in tscv.split(self.full_history_data):
            # 创建临时评估函数，只使用训练集数据
            def cv_evaluate(params):
                # 保存原始数据
                original_data = self.full_history_data
                # 使用训练集数据
                self.full_history_data = original_data[train_index]
                # 评估
                score = base_evaluate(params)
                # 恢复原始数据
                self.full_history_data = original_data
                return score
            
            # 训练集评估
            train_score = cv_evaluate(params)
            train_scores.append(train_score)
            
            # 验证集评估
            def val_evaluate(params):
                original_data = self.full_history_data
                self.full_history_data = original_data[test_index]
                score = base_evaluate(params)
                self.full_history_data = original_data
                return score
            
            val_score = val_evaluate(params)
            val_scores.append(val_score)
        
        # 返回平均训练分数和平均验证分数
        return np.mean(train_scores), np.mean(val_scores)
        
    def _weight_regularization(self, params, lambda_reg=0.01):
        """权重正则化，惩罚极端权重值"""
        weights = [
            params['co_weight'],
            params['digit_transition_weight'],
            params['sum_transition_weight'],
            params['digit_exclusion_weight']
        ]
        # 计算权重的方差作为正则化项
        weight_variance = np.var(weights)
        # 计算权重的L2范数作为正则化项
        l2_norm = np.sqrt(np.sum(np.square(weights)))
        # 综合正则化惩罚
        return lambda_reg * (weight_variance + l2_norm)

    def stop_optimization(self):
        """停止优化过程"""
        self.optimization_running = False

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def _generate_random_params(self):
        """生成随机参数组合"""
        params = {}
        for param, (min_val, max_val) in self.param_ranges.items():
            if param in ['co_weight', 'digit_transition_weight', 'sum_transition_weight', 'digit_exclusion_weight']:
                continue
            # 使用更细致的随机生成
            if param in ['alpha', 'lambda']:
                # 对于这些参数使用对数分布
                log_min = np.log(min_val)
                log_max = np.log(max_val)
                value = np.exp(random.uniform(log_min, log_max))
            elif param in ['window', 'periodicity', 'selection_count']:
                # 整数参数使用随机整数生成
                value = random.randint(int(min_val), int(max_val))
            else:
                value = random.uniform(min_val, max_val)
            params[param] = value
        
        # 使用Dirichlet分布生成权重，确保和为1
        while True:
            # 使用不同的alpha值来控制分布
            weights = np.random.dirichlet(np.array([4, 3, 2, 1]))  # co, digit_transition, sum_transition, digit_exclusion
            co_w, digit_w, sum_w, exclusion_w = weights

            # 检查是否在允许范围内
            if (self.param_ranges['co_weight'][0] <= co_w <= self.param_ranges['co_weight'][1] and
                self.param_ranges['digit_transition_weight'][0] <= digit_w <= self.param_ranges['digit_transition_weight'][1] and
                self.param_ranges['sum_transition_weight'][0] <= sum_w <= self.param_ranges['sum_transition_weight'][1] and
                self.param_ranges['digit_exclusion_weight'][0] <= exclusion_w <= self.param_ranges['digit_exclusion_weight'][1]):
                params['co_weight'] = co_w
                params['digit_transition_weight'] = digit_w
                params['sum_transition_weight'] = sum_w
                params['digit_exclusion_weight'] = exclusion_w
                break
        
        return params
    
    def _crossover(self, parent1, parent2):
        """参数交叉"""
        if random.random() > self.crossover_rate:
            return parent1.copy()
            
        child = {}
        # 对非权重参数进行均匀交叉
        for param in parent1:
            if param not in ['co_weight', 'digit_transition_weight', 'sum_transition_weight', 'digit_exclusion_weight']:
                if random.random() < 0.5:
                    child[param] = parent1[param]
                else:
                    child[param] = parent2[param]

        # 对权重参数进行特殊处理
        weights1 = np.array([parent1['co_weight'], parent1['digit_transition_weight'], parent1['sum_transition_weight'], parent1['digit_exclusion_weight']])
        weights2 = np.array([parent2['co_weight'], parent2['digit_transition_weight'], parent2['sum_transition_weight'], parent2['digit_exclusion_weight']])

        # 使用算术交叉并确保和为1
        alpha = random.random()
        new_weights = alpha * weights1 + (1 - alpha) * weights2

        # 归一化确保和为1
        new_weights = new_weights / np.sum(new_weights)

        # 检查是否在允许范围内，如果不在则重新生成
        if not (self.param_ranges['co_weight'][0] <= new_weights[0] <= self.param_ranges['co_weight'][1] and
            self.param_ranges['digit_transition_weight'][0] <= new_weights[1] <= self.param_ranges['digit_transition_weight'][1] and
            self.param_ranges['sum_transition_weight'][0] <= new_weights[2] <= self.param_ranges['sum_transition_weight'][1] and
            self.param_ranges['digit_exclusion_weight'][0] <= new_weights[3] <= self.param_ranges['digit_exclusion_weight'][1]):
            # 如果超出范围，使用Dirichlet分布重新生成
            new_weights = np.random.dirichlet(np.array([4, 3, 2, 1]))

        child['co_weight'] = new_weights[0]
        child['digit_transition_weight'] = new_weights[1]
        child['sum_transition_weight'] = new_weights[2]
        child['digit_exclusion_weight'] = new_weights[3]
        
        return child
    
    def _mutate(self, params):
        """参数变异"""
        mutated = params.copy()
        
        # 对每个非权重参数进行变异
        for param in params:
            if param not in ['co_weight', 'digit_transition_weight', 'sum_transition_weight', 'digit_exclusion_weight']:
                if random.random() < self.mutation_rate:
                    min_val, max_val = self.param_ranges[param]
                    current_val = params[param]

                    # 使用正态分布进行变异
                    sigma = (max_val - min_val) * 0.1  # 标准差为范围的10%
                    new_val = random.gauss(current_val, sigma)

                    # 确保在范围内
                    new_val = max(min_val, min(max_val, new_val))
                    # 整数参数特殊处理
                    if param in ['window', 'periodicity', 'selection_count']:
                        new_val = round(new_val)
                    mutated[param] = new_val

        # 权重参数的变异
        if random.random() < self.mutation_rate:
            weights = np.array([mutated['co_weight'], mutated['digit_transition_weight'], mutated['sum_transition_weight'], mutated['digit_exclusion_weight']])

            # 使用Dirichlet分布进行变异
            # 使用当前权重作为基准，添加扰动
            alpha = weights * 10  # 控制变异程度
            new_weights = np.random.dirichlet(alpha)

            # 检查是否在允许范围内
            if (self.param_ranges['co_weight'][0] <= new_weights[0] <= self.param_ranges['co_weight'][1] and
                self.param_ranges['digit_transition_weight'][0] <= new_weights[1] <= self.param_ranges['digit_transition_weight'][1] and
                self.param_ranges['sum_transition_weight'][0] <= new_weights[2] <= self.param_ranges['sum_transition_weight'][1] and
                self.param_ranges['digit_exclusion_weight'][0] <= new_weights[3] <= self.param_ranges['digit_exclusion_weight'][1]):
                mutated['co_weight'] = new_weights[0]
                mutated['digit_transition_weight'] = new_weights[1]
                mutated['sum_transition_weight'] = new_weights[2]
                mutated['digit_exclusion_weight'] = new_weights[3]
        
        return mutated
    
    def _tournament_selection(self, evaluated_population):
        """锦标赛选择法"""
        tournament_size = max(3, self.population_size // 10)
        participants = random.sample(evaluated_population, tournament_size)
        return max(participants, key=lambda x: x[1])[0]
    
    def get_optimization_summary(self):
        """获取优化过程的摘要信息"""
        if not self.optimization_history:
            return "尚未进行优化"
        
        summary = []
        summary.append("优化过程摘要:")
        summary.append("-" * 50)
        
        # 按命中率排序
        sorted_history = sorted(self.optimization_history, key=lambda x: x['hit_rate'], reverse=True)
        
        # 输出前5个最佳结果
        for i, record in enumerate(sorted_history[:5], 1):
            summary.append(f"\n第{i}优结果 (迭代{record['iteration']}):")
            summary.append(f"命中率: {record['hit_rate']*100:.2f}%")
            summary.append("参数:")
            for param, value in record['params'].items():
                summary.append(f"  {param}: {value:.3f}")
            summary.append("-" * 30)
        
        return "\n".join(summary)

class LotteryPredictionApp:
    def __init__(self, root):
        self.root = root
        self.root.title("第二套 (智能预测系统)")
        self.root.geometry("1400x900")
        self.root.configure(bg="#f0f0f0")
        
        # 加载配置文件中的参数
        self.config = load_config()
        self.default_params = {k: v for k, v in self.config.items() if k != 'last_excel_file'}
        
        # 初始化数据文件路径，默认为F:/pl5/data.xlsx
        self.data_file = "F:/pl5/data.xlsx"
        self.file_path_var = tk.StringVar(value=self.data_file)
        
        # 初始化变量
        self.status_var = tk.StringVar(value="就绪")
        self.backtest_status_var = tk.StringVar(value="回测完成")
        self.backtest_periods_var = tk.StringVar(value="30")
        self.drawdown_days_var = tk.StringVar(value="0")
        
        # 数据存储
        self.history_data = None
        self.original_history_data = None
        self.original_periods_data = None
        self.periods_data = None
        self.last_period = None
        self.predictions = None
        self.model = None
        
        # 添加预测历史记录存储
        self.prediction_history = {}  # 存储历史预测结果
        
        # 添加线程管理
        self.running_threads = []
        
        # 创建界面
        self.create_widgets()
        
        # 自动加载上次的文件
        self.root.after(1000, self.delayed_auto_load)
    
    def on_closing(self):
        """关闭程序时清理线程"""
        try:
            # 停止所有运行中的线程
            for thread in self.running_threads:
                if thread.is_alive():
                    thread.join(timeout=1.0)
            self.root.destroy()
        except Exception as e:
            print(f"关闭程序时发生错误: {e}")
            self.root.destroy()
    
    def delayed_auto_load(self):
        """延迟自动加载数据文件"""
        try:
            # 检查是否存在默认数据文件
            if os.path.exists(self.data_file):
                # 更新状态显示
                self.status_var.set("发现数据文件，准备自动加载...")
                self.root.update()
                # 再次延迟一点时间确保界面完全就绪
                self.root.after(500, self.safe_auto_load)
            else:
                self.status_var.set("未找到F:/pl5/data.xlsx文件，请手动选择Excel文件")
        except Exception as e:
            print(f"延迟加载检查时发生错误: {e}")
            self.status_var.set("就绪")
    
    def safe_auto_load(self):
        """安全地自动加载数据文件"""
        try:
            # 确保文件路径变量已设置
            if self.file_path_var.get() and os.path.exists(self.file_path_var.get()):
                self.status_var.set("正在自动加载数据...")
                self.root.update()
                # 在新线程中执行，避免阻塞界面
                threading.Thread(target=self._safe_auto_backtest).start()
            else:
                self.status_var.set("就绪")
        except Exception as e:
            print(f"安全自动加载时发生错误: {e}")
            self.status_var.set("就绪")
    
    def _safe_auto_backtest(self):
        """安全的自动回测线程"""
        try:
            # 添加异常处理的自动回测
            backtest_periods = 30  # 使用默认回测期数
            self._run_backtest_and_predict(backtest_periods)
        except Exception as e:
            # 在主线程中显示错误
            self.root.after(0, lambda: self.status_var.set("自动加载失败，请手动操作"))
            print(f"自动回测时发生错误: {e}")
    
    def auto_load_last_file(self):
        """自动加载上次使用的Excel文件"""
        last_file = self.config.get('last_excel_file', '')
        if last_file and os.path.exists(last_file):
            self.file_path_var.set(last_file)
            # 自动执行回测和预测
            self.run_backtest_and_predict()
    
    def create_widgets(self):
        # 顶部框架 - 文件选择
        top_frame = tk.Frame(self.root, bg="#f0f0f0", padx=20, pady=10)
        top_frame.pack(fill=tk.X)
        
        file_label = tk.Label(top_frame, text="选择Excel文件:", bg="#f0f0f0", font=("SimHei", 10))
        file_label.pack(side=tk.LEFT, padx=5)
        
        file_entry = tk.Entry(top_frame, textvariable=self.file_path_var, width=50)
        file_entry.pack(side=tk.LEFT, padx=5)
        
        browse_btn = tk.Button(top_frame, text="浏览", command=self.browse_file, font=("SimHei", 10),
                              bg="#4CAF50", fg="white", relief=tk.RAISED, padx=10)
        browse_btn.pack(side=tk.LEFT, padx=5)
        
        predict_btn = tk.Button(top_frame, text="回测并预测", command=self.run_backtest_and_predict, font=("SimHei", 10),
                               bg="#2196F3", fg="white", relief=tk.RAISED, padx=10)
        predict_btn.pack(side=tk.LEFT, padx=5)
        
        params_btn = tk.Button(top_frame, text="参数设置", command=self.show_params_dialog, font=("SimHei", 10),
                              bg="#FF9800", fg="white", relief=tk.RAISED, padx=10)
        params_btn.pack(side=tk.LEFT, padx=5)
        
        optimize_btn = tk.Button(top_frame, text="自动优化参数", command=self.auto_optimize_params, font=("SimHei", 10),
                              bg="#FF5722", fg="white", relief=tk.RAISED, padx=10)
        optimize_btn.pack(side=tk.LEFT, padx=5)
        
        # 添加初始化参数按钮
        init_btn = tk.Button(top_frame, text="初始化参数", command=self.init_params, font=("SimHei", 10),
                           bg="#673AB7", fg="white", relief=tk.RAISED, padx=10)
        init_btn.pack(side=tk.LEFT, padx=5)
        
        # 添加输入今日开奖号码的按钮
        add_result_btn = tk.Button(top_frame, text="录入开奖号码", command=self.show_add_result_dialog, font=("SimHei", 10),
                                bg="#673AB7", fg="white", relief=tk.RAISED, padx=10)
        add_result_btn.pack(side=tk.LEFT, padx=5)
        
        # 添加修改开奖号码的按钮
        edit_result_btn = tk.Button(top_frame, text="修改开奖号码", command=self.show_edit_result_dialog, font=("SimHei", 10),
                                 bg="#009688", fg="white", relief=tk.RAISED, padx=10)
        edit_result_btn.pack(side=tk.LEFT, padx=5)
        
        # 添加测试预测一致性按钮
        test_btn = tk.Button(top_frame, text="测试预测一致性", command=self.test_prediction_consistency, font=("SimHei", 10),
                           bg="#E91E63", fg="white", relief=tk.RAISED, padx=10)
        test_btn.pack(side=tk.LEFT, padx=5)
        
        # 添加详细调试按钮
        debug_btn = tk.Button(top_frame, text="详细调试", command=self.detailed_debug, font=("SimHei", 10),
                            bg="#9C27B0", fg="white", relief=tk.RAISED, padx=10)
        debug_btn.pack(side=tk.LEFT, padx=5)
        
        # 中间框架 - 主内容区域
        middle_frame = tk.Frame(self.root, bg="#f5f5f5", padx=15, pady=8)
        middle_frame.pack(fill=tk.BOTH, expand=True)
        
        # 回测设置框架 - 添加标题和边框
        backtest_settings_frame = tk.Frame(middle_frame, bg="white", relief=tk.RAISED, bd=1)
        backtest_settings_frame.pack(fill=tk.X, pady=(0, 8))
        
        # 回测设置标题
        backtest_title_frame = tk.Frame(backtest_settings_frame, bg="#2196F3", height=30)
        backtest_title_frame.pack(fill=tk.X)
        backtest_title_frame.pack_propagate(False)
        
        tk.Label(backtest_title_frame, text="回测参数设置", font=("SimHei", 11, "bold"), 
                bg="#2196F3", fg="white").pack(side=tk.LEFT, padx=15, pady=5)
        
        # 回测设置内容
        backtest_content_frame = tk.Frame(backtest_settings_frame, bg="white", padx=15, pady=8)
        backtest_content_frame.pack(fill=tk.X)
        
        backtest_label = tk.Label(backtest_content_frame, text="回测期数:", bg="white", font=("SimHei", 10))
        backtest_label.pack(side=tk.LEFT, padx=5)
        
        self.backtest_periods_var = tk.StringVar(value="30")
        backtest_entry = tk.Entry(backtest_content_frame, textvariable=self.backtest_periods_var, width=10)
        backtest_entry.pack(side=tk.LEFT, padx=5)
        
        # 添加回撤天数输入框
        drawdown_label = tk.Label(backtest_content_frame, text="回撤天数:", bg="white", font=("SimHei", 10))
        drawdown_label.pack(side=tk.LEFT, padx=5)
        
        self.drawdown_days_var = tk.StringVar(value="0")
        drawdown_entry = tk.Entry(backtest_content_frame, textvariable=self.drawdown_days_var, width=10)
        drawdown_entry.pack(side=tk.LEFT, padx=5)
        
        backtest_btn = tk.Button(backtest_content_frame, text="单独回测", command=self.run_backtest, 
                                font=("SimHei", 10), bg="#FF5722", fg="white", relief=tk.RAISED, padx=10)
        backtest_btn.pack(side=tk.LEFT, padx=5)
        
        self.backtest_status_var = tk.StringVar()
        self.backtest_status_var.set("请先选择文件并开始回测")
        backtest_status_label = tk.Label(backtest_content_frame, textvariable=self.backtest_status_var, 
                                        bg="white", font=("SimHei", 10))
        backtest_status_label.pack(side=tk.LEFT, padx=10)
        
        # 主内容框架 - 包含预测结果和回测结果
        self.main_content_frame = tk.Frame(middle_frame, bg="white", relief=tk.RAISED, bd=1)
        self.main_content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 8))
        
        # 主内容标题
        main_title_frame = tk.Frame(self.main_content_frame, bg="#4CAF50", height=30)
        main_title_frame.pack(fill=tk.X)
        main_title_frame.pack_propagate(False)
        
        tk.Label(main_title_frame, text="智能预测系统 - 分析结果", font=("SimHei", 11, "bold"), 
                bg="#4CAF50", fg="white").pack(side=tk.LEFT, padx=15, pady=5)
        
        # 内容区域 - 使用PanedWindow来分割预测和回测区域
        content_paned = tk.PanedWindow(self.main_content_frame, orient=tk.HORIZONTAL, bg="white")
        content_paned.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 预测结果框架
        self.result_frame = tk.Frame(content_paned, bg="white", relief=tk.SUNKEN, bd=1)
        content_paned.add(self.result_frame, width=400)
        
        # 回测结果框架 - 添加滚动条支持
        self.backtest_result_frame = tk.Frame(content_paned, bg="white", relief=tk.SUNKEN, bd=1)
        content_paned.add(self.backtest_result_frame, width=600)
        
        # 创建Canvas和Scrollbar
        # 创建Canvas和滚动条（垂直+水平）
        self.backtest_canvas = tk.Canvas(self.backtest_result_frame, bg="white", highlightthickness=0)
        self.vscrollbar = tk.Scrollbar(self.backtest_result_frame, orient="vertical", command=self.backtest_canvas.yview)
        self.hscrollbar = tk.Scrollbar(self.backtest_result_frame, orient="horizontal", command=self.backtest_canvas.xview)
        self.backtest_scrollable_frame = tk.Frame(self.backtest_canvas, bg="white")

        # 配置Canvas滚动区域
        # 配置Canvas滚动区域
        # 绑定滚动区域和框架宽度调整
        def on_frame_configure(e):
            self.backtest_canvas.configure(scrollregion=self.backtest_canvas.bbox("all"))
        self.backtest_scrollable_frame.bind("<Configure>", on_frame_configure)

        def on_canvas_configure(e):
            # 设置框架宽度以匹配Canvas
            self.backtest_canvas.itemconfig(frame_id, width=e.width)
        self.backtest_canvas.bind("<Configure>", on_canvas_configure)

        # 创建窗口并保存ID
        frame_id = self.backtest_canvas.create_window((0, 0), window=self.backtest_scrollable_frame, anchor="nw")
        self.backtest_canvas.configure(yscrollcommand=self.vscrollbar.set, xscrollcommand=self.hscrollbar.set)

        # 放置滚动条和Canvas
        self.vscrollbar.pack(side="right", fill="y")
        self.hscrollbar.pack(side="bottom", fill="x")
        self.backtest_canvas.pack(side="left", fill="both", expand=True)
        
        # 底部状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("系统就绪 - 请选择数据文件开始分析")
        status_bar = tk.Label(self.root, textvariable=self.status_var, bd=1, relief=tk.SUNKEN, 
                             anchor=tk.W, bg="#E3F2FD", fg="#1976D2", font=("SimHei", 9))
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def browse_file(self):
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls")],
            initialfile="F:/pl5/data.xlsx"  # 设置默认文件名
        )
        if file_path:
            self.file_path_var.set(file_path)
            # 保存文件路径到配置
            self.config['last_excel_file'] = file_path
            save_config(self.config)
            # 更新数据文件路径
            self.data_file = file_path
    
    def load_data(self, file_path):
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)
            
            # 验证数据格式
            required_columns = ['期号', '千位', '百位', '十位', '个位', '球五']
            if not all(col in df.columns for col in required_columns):
                raise ValueError("Excel文件缺少必要的列")
            
            # 获取最后一期期号
            last_period = df['期号'].iloc[-1]
            
            # 提取期号数据
            periods_data = df['期号'].values
            
            # 提取开奖数据
            data = df[['千位', '百位', '十位', '个位', '球五']].values
            
            # 验证数据范围
            if not np.all((data >= 0) & (data <= 9)):
                raise ValueError("开奖数据包含无效数字（应在0-9范围内）")
                
            return data, len(data), last_period, periods_data
        
        except Exception as e:
            messagebox.showerror("数据加载错误", str(e))
            return None, 0, None, None
    
    def run_backtest_and_predict(self):
        """执行回测并基于回测结果进行预测"""
        file_path = self.file_path_var.get()
        if file_path == "未选择文件":
            messagebox.showwarning("警告", "请先选择Excel文件")
            return
        
        try:
            backtest_periods = int(self.backtest_periods_var.get())
            if backtest_periods <= 0:
                messagebox.showwarning("警告", "回测期数必须大于0")
                return
        except ValueError:
            messagebox.showwarning("警告", "请输入有效的回测期数")
            return
            
        try:
            drawdown_days = int(self.drawdown_days_var.get())
            if drawdown_days < 0:
                messagebox.showwarning("警告", "回撤天数不能为负数")
                return
        except ValueError:
            messagebox.showwarning("警告", "请输入有效的回撤天数")
            return
            
        # 在新线程中运行回测和预测，避免界面卡顿
        self.status_var.set("正在加载数据...")
        self.root.update()
        
        threading.Thread(target=self._run_backtest_and_predict, args=(backtest_periods, drawdown_days)).start()
    
    def _run_backtest_and_predict(self, backtest_periods, drawdown_days=0):
        try:
            # 加载数据
            data, num_periods, last_period, periods_data = self.load_data(self.file_path_var.get())
            if data is None:
                self.status_var.set("就绪")
                return
                
            # 保存原始数据副本
            self.original_history_data = data.copy()
            self.original_periods_data = periods_data.copy()
            
            self.history_data = data
            self.last_period = last_period  # 保存最后一期期号
            self.periods_data = periods_data  # 保存期号数据
            
            # 应用回撤天数，忽略最新的数据
            if drawdown_days > 0:
                if drawdown_days >= len(data):
                    messagebox.showwarning("警告", f"回撤天数({drawdown_days})不能大于等于总数据量({len(data)})")
                    return
                data = data[:-drawdown_days]
                periods_data = periods_data[:-drawdown_days]
                # messagebox.showinfo("提示", f"已忽略最新{drawdown_days}条数据，剩余数据量: {len(data)}")
            
            # 检查回测期数是否合理
            if backtest_periods >= len(data):
                backtest_periods = len(data) - 1
                self.backtest_periods_var.set(str(backtest_periods))
                # messagebox.showinfo("提示", f"回测期数已自动调整为: {backtest_periods}")
            
            # 执行回测
            self.status_var.set("正在进行回测...")
            self.backtest_status_var.set("正在进行回测...")
            
            total_periods = len(self.history_data)
            hit_rates = []
            full_hit_count = 0
            
            # 调试信息
            print(f"回测开始 - 总数据量: {total_periods}")
            print(f"期号数据类型: {type(self.periods_data[0])}")
            print(f"前5个期号: {self.periods_data[:5]}")
            print(f"后5个期号: {self.periods_data[-5:]}")
            
            # 定义两两组合
            position_pairs = [
                ('千位', '百位', 0, 1), ('千位', '十位', 0, 2), ('千位', '个位', 0, 3),
                ('百位', '十位', 1, 2), ('百位', '个位', 1, 3),
                ('十位', '个位', 2, 3)
            ]
            
            # 初始化两两组合统计
            pair_stats = {}
            for pair_name1, pair_name2, pos1, pos2 in position_pairs:
                pair_key = f"{pair_name1[0]}{pair_name2[0]}"
                pair_stats[pair_key] = {'hit_count': 0, 'total_count': 0}
            
            backtest_results = []
            
            # 对每一期进行回测
            for i in range(total_periods - backtest_periods, total_periods):
                # 获取当前期号
                current_period = self.periods_data[i]
                
                # 获取实际开奖号码
                actual_numbers = self.history_data[i]
                
                # 准备训练数据（不包括当前期）
                train_data = self.history_data[:i]
                
                # 为每一期创建新的模型实例，确保状态完全独立
                current_model = MFTNModel(self.get_current_params())
                
                # 训练模型
                current_model.fit(train_data)
                
                # 预测 - 使用与正式预测相同的参数
                predictions = current_model.predict_next(is_predict=True)
                
                # 计算命中情况
                position_hits = []
                for pos in range(4):  # 只比较前4位
                    predicted_nums = predictions[pos]
                    actual_num = actual_numbers[pos]
                    hit = actual_num in predicted_nums
                    position_hits.append(hit)
                
                # 计算两两组合的命中情况
                pair_hits = {}
                for pair_name1, pair_name2, pos1, pos2 in position_pairs:
                    if pos1 < 4 and pos2 < 4:  # 只考虑前4位的组合
                        pair_key = f"{pair_name1[0]}{pair_name2[0]}"
                        hit1 = actual_numbers[pos1] in predictions[pos1]
                        hit2 = actual_numbers[pos2] in predictions[pos2]
                        pair_hit = hit1 and hit2
                        pair_hits[pair_key] = pair_hit
                        
                        # 更新统计
                        pair_stats[pair_key]['total_count'] += 1
                        if pair_hit:
                            pair_stats[pair_key]['hit_count'] += 1
                
                # 计算当前期的命中率
                hit_rate = sum(position_hits) / 4.0
                hit_rates.append(hit_rate)
                
                # 检查是否4位全中
                if all(position_hits):
                    full_hit_count += 1
                
                # 保存回测结果
                backtest_results.append({
                    'period': current_period,
                    'actual': actual_numbers,
                    'predictions': predictions,
                    'position_hits': position_hits,
                    'pair_hits': pair_hits,
                    'hit_rate': hit_rate
                })
                
                # 更新状态
                progress = (i - (total_periods - backtest_periods) + 1) / backtest_periods * 100
                self.root.after(0, self.backtest_status_var.set, 
                               f"正在进行回测: {i - (total_periods - backtest_periods) + 1}/{backtest_periods} ({progress:.1f}%)")
                self.root.update_idletasks()  # 强制更新UI
            
            # 计算总体命中率和4位全中率
            overall_hit_rate = sum(hit_rates) / len(hit_rates)
            full_hit_rate = full_hit_count / len(hit_rates)
            
            # 保存回测结果到类属性
            self.backtest_results = backtest_results
            
            # 更新UI显示回测结果
            self.root.after(0, self._update_backtest_display, backtest_results, overall_hit_rate, full_hit_rate, pair_stats)
            
            # 使用全部数据训练模型并进行预测
            self.status_var.set("正在训练模型...")
            self.root.update()
            
            # 使用当前参数初始化模型
            self.model = MFTNModel(self.get_current_params())
            
            # 训练模型
            self.model.fit(self.history_data)
            
            self.status_var.set("正在预测...")
            self.root.update()
            
            # 预测
            self.predictions = self.model.predict_next(is_predict=True)
            
            # 更新UI显示预测结果
            self.root.after(0, self._update_result_display, num_periods)
            
            # 显示回测结果摘要
            pair_summary = "\n".join([f"{k}: {v['hit_count']}/{v['total_count']} ({v['hit_count']/v['total_count']*100:.1f}%)" 
                                    for k, v in pair_stats.items()])
            # self.root.after(0, messagebox.showinfo, "回测结果", 
            #                f"回测完成!\n回测期数: {len(backtest_results)}\n平均命中率: {overall_hit_rate*100:.2f}%\n4位全中率: {full_hit_rate*100:.2f}%\n\n两两组合命中率:\n{pair_summary}")
            
        except Exception as e:
            self.root.after(0, messagebox.showerror, "错误", f"回测或预测过程中发生错误: {str(e)}")
        finally:
            self.root.after(0, self.status_var.set, "就绪")
            self.root.after(0, self.backtest_status_var.set, "回测完成")
    
    def run_backtest(self):
        """仅执行回测，不进行预测"""
        if self.history_data is None:
            messagebox.showwarning("警告", "请先加载数据")
            return
            
        try:
            backtest_periods = int(self.backtest_periods_var.get())
            if backtest_periods <= 0:
                messagebox.showwarning("警告", "回测期数必须大于0")
                return
        except ValueError:
            messagebox.showwarning("警告", "请输入有效的回测期数")
            return
            
        try:
            drawdown_days = int(self.drawdown_days_var.get())
            if drawdown_days < 0:
                messagebox.showwarning("警告", "回撤天数不能为负数")
                return
        except ValueError:
            messagebox.showwarning("警告", "请输入有效的回撤天数")
            return
            
        # 在新线程中运行回测，避免界面卡顿
        self.backtest_status_var.set("正在进行回测...")
        threading.Thread(target=self._run_backtest, args=(backtest_periods, drawdown_days)).start()
    
    def _run_backtest(self, backtest_periods, drawdown_days=0):
        try:
            # 确保有原始数据
            if self.original_history_data is None:
                messagebox.showwarning("警告", "请先加载数据")
                return
            
            # 从原始数据创建工作副本
            if drawdown_days > 0:
                if drawdown_days >= len(self.original_history_data):
                    messagebox.showwarning("警告", f"回撤天数({drawdown_days})不能大于等于总数据量({len(self.original_history_data)})")
                    return
                working_data = self.original_history_data[:-drawdown_days].copy()
                working_periods = self.original_periods_data[:-drawdown_days].copy()
                # messagebox.showinfo("提示", f"已忽略最新{drawdown_days}条数据，剩余数据量: {len(working_data)}")
            else:
                working_data = self.original_history_data.copy()
                working_periods = self.original_periods_data.copy()
            
            total_periods = len(working_data)
            
            # 检查回测期数是否合理
            if backtest_periods >= total_periods:
                messagebox.showwarning("警告", f"回测期数({backtest_periods})不能大于等于剩余数据量({total_periods})")
                return
            
            hit_rates = []
            full_hit_count = 0
            
            # 定义两两组合
            position_pairs = [
                ('千位', '百位', 0, 1), ('千位', '十位', 0, 2), ('千位', '个位', 0, 3),
                ('百位', '十位', 1, 2), ('百位', '个位', 1, 3),
                ('十位', '个位', 2, 3)
            ]
            
            # 初始化两两组合统计
            pair_stats = {}
            for pair_name1, pair_name2, pos1, pos2 in position_pairs:
                pair_key = f"{pair_name1[0]}{pair_name2[0]}"
                pair_stats[pair_key] = {'hit_count': 0, 'total_count': 0}
            
            backtest_results = []
            
            # 使用当前参数初始化模型
            current_model = MFTNModel(self.get_current_params())
            
            # 对每一期进行回测
            for i in range(total_periods - backtest_periods, total_periods):
                # 获取当前期号
                current_period = working_periods[i]
                
                # 获取实际开奖号码
                actual_numbers = working_data[i]
                
                # 准备训练数据（不包括当前期）
                train_data = working_data[:i]
                
                # 训练模型
                current_model.fit(train_data)
                
                # 预测 - 使用与正式预测相同的参数
                predictions = current_model.predict_next(is_predict=True)
                
                # 计算命中情况
                position_hits = []
                for pos in range(4):  # 只比较前4位
                    predicted_nums = predictions[pos]
                    actual_num = actual_numbers[pos]
                    hit = actual_num in predicted_nums
                    position_hits.append(hit)
                
                # 计算两两组合的命中情况
                pair_hits = {}
                for pair_name1, pair_name2, pos1, pos2 in position_pairs:
                    if pos1 < 4 and pos2 < 4:  # 只考虑前4位的组合
                        pair_key = f"{pair_name1[0]}{pair_name2[0]}"
                        hit1 = actual_numbers[pos1] in predictions[pos1]
                        hit2 = actual_numbers[pos2] in predictions[pos2]
                        pair_hit = hit1 and hit2
                        pair_hits[pair_key] = pair_hit
                        
                        # 更新统计
                        pair_stats[pair_key]['total_count'] += 1
                        if pair_hit:
                            pair_stats[pair_key]['hit_count'] += 1
                
                # 计算当前期的命中率
                hit_rate = sum(position_hits) / 4.0
                hit_rates.append(hit_rate)
                
                # 检查是否4位全中
                if all(position_hits):
                    full_hit_count += 1
                
                # 保存回测结果
                backtest_results.append({
                    'period': current_period,
                    'actual': actual_numbers,
                    'predictions': predictions,
                    'position_hits': position_hits,
                    'pair_hits': pair_hits,
                    'hit_rate': hit_rate
                })
                
                # 更新状态
                progress = (i - (total_periods - backtest_periods) + 1) / backtest_periods * 100
                self.root.after(0, self.backtest_status_var.set, 
                               f"正在进行回测: {i - (total_periods - backtest_periods) + 1}/{backtest_periods} ({progress:.1f}%)")
            
            # 计算总体命中率和4位全中率
            overall_hit_rate = sum(hit_rates) / len(hit_rates)
            full_hit_rate = full_hit_count / len(hit_rates)
            
            # 保存回测结果到类属性
            self.backtest_results = backtest_results
            
            # 更新UI显示回测结果
            self.root.after(0, self._update_backtest_display, backtest_results, overall_hit_rate, full_hit_rate, pair_stats)
            
            # 显示回测结果摘要
            pair_summary = "\n".join([f"{k}: {v['hit_count']}/{v['total_count']} ({v['hit_count']/v['total_count']*100:.1f}%)" 
                                    for k, v in pair_stats.items()])
            # self.root.after(0, messagebox.showinfo, "回测结果", 
            #                f"回测完成!\n回测期数: {len(backtest_results)}\n平均命中率: {overall_hit_rate*100:.2f}%\n4位全中率: {full_hit_rate*100:.2f}%\n\n两两组合命中率:\n{pair_summary}")
            
        except Exception as e:
            self.root.after(0, messagebox.showerror, "错误", f"回测过程中发生错误: {str(e)}")
        finally:
            self.root.after(0, self.backtest_status_var.set, "回测完成")
    
    def _update_backtest_display(self, results, overall_hit_rate, full_hit_rate, pair_stats):
        # 清除现有结果
        for widget in self.backtest_scrollable_frame.winfo_children():
            widget.destroy()
        
        # 回测结果标题
        backtest_title_frame = tk.Frame(self.backtest_scrollable_frame, bg="#673AB7", height=25)
        backtest_title_frame.pack(fill=tk.X)
        backtest_title_frame.pack_propagate(False)
        
        tk.Label(backtest_title_frame, text="📈 回测分析结果", font=("SimHei", 10, "bold"), 
                bg="#673AB7", fg="white").pack(side=tk.LEFT, padx=10, pady=3)
        
        # 回测结果内容框架
        backtest_content_frame = tk.Frame(self.backtest_scrollable_frame, bg="white", padx=10, pady=8)
        backtest_content_frame.pack(fill=tk.BOTH, expand=True)
        
        # 显示当前使用的参数（简化显示）
        params = self.get_current_params()
        key_params = {k: v for k, v in params.items() if k in ['lambda', 'co_weight', 'digit_transition_weight', 'sum_transition_weight', 'digit_exclusion_weight']}
        params_text = "回测参数: " + ", ".join([f"{k}={v:.3f}" for k, v in key_params.items()])
        params_label = tk.Label(backtest_content_frame, text=params_text, font=("SimHei", 9), 
                               bg="#F5F5F5", fg="#666", pady=3, padx=8)
        params_label.pack(fill=tk.X)
        
        # 显示回测统计信息
        stats_frame = tk.Frame(backtest_content_frame, bg="white")
        stats_frame.pack(fill=tk.X, pady=8)
        
        # 创建统计卡片
        stats_cards_frame = tk.Frame(stats_frame, bg="white")
        stats_cards_frame.pack(fill=tk.X)
        
        # 回测期数卡片
        period_card = tk.Frame(stats_cards_frame, bg="#E3F2FD", relief=tk.RAISED, bd=1, padx=15, pady=8)
        period_card.pack(side=tk.LEFT, padx=5)
        tk.Label(period_card, text=f"📊 回测期数", font=("SimHei", 9), bg="#E3F2FD", fg="#1976D2").pack()
        tk.Label(period_card, text=f"{len(results)}", font=("SimHei", 12, "bold"), bg="#E3F2FD", fg="#1976D2").pack()
        
        # 平均命中率卡片
        hit_rate_card = tk.Frame(stats_cards_frame, bg="#E8F5E8", relief=tk.RAISED, bd=1, padx=15, pady=8)
        hit_rate_card.pack(side=tk.LEFT, padx=5)
        tk.Label(hit_rate_card, text=f"🎯 平均命中率", font=("SimHei", 9), bg="#E8F5E8", fg="#388E3C").pack()
        tk.Label(hit_rate_card, text=f"{overall_hit_rate*100:.2f}%", font=("SimHei", 12, "bold"), bg="#E8F5E8", fg="#388E3C").pack()
        
        # 4位全中率卡片
        full_hit_card = tk.Frame(stats_cards_frame, bg="#FFF3E0", relief=tk.RAISED, bd=1, padx=15, pady=8)
        full_hit_card.pack(side=tk.LEFT, padx=5)
        tk.Label(full_hit_card, text=f"🏆 4位全中率", font=("SimHei", 9), bg="#FFF3E0", fg="#F57C00").pack()
        tk.Label(full_hit_card, text=f"{full_hit_rate*100:.2f}%", font=("SimHei", 12, "bold"), bg="#FFF3E0", fg="#F57C00").pack()
        
        # 添加两两组合统计表格
        pair_stats_frame = tk.Frame(backtest_content_frame, bg="white")
        pair_stats_frame.pack(fill=tk.BOTH, expand=True, pady=8)

        # 两两组合统计标题
        pair_title_frame = tk.Frame(pair_stats_frame, bg="#9C27B0", height=25)
        pair_title_frame.pack(fill=tk.X)
        pair_title_frame.pack_propagate(False)
        
        tk.Label(pair_title_frame, text="🔗 两两组合命中率统计", font=("SimHei", 10, "bold"), 
                bg="#9C27B0", fg="white").pack(side=tk.LEFT, padx=10, pady=3)

        # 创建两两组合统计表格
        pair_table_frame = tk.Frame(pair_stats_frame, bg="white", padx=10, pady=5)
        pair_table_frame.pack(fill=tk.BOTH, expand=True)
        
        # 表头
        pair_headers = ["组合", "命中次数", "总次数", "命中率", "状态"]
        for col, header in enumerate(pair_headers):
            tk.Label(pair_table_frame, text=header, font=("SimHei", 9, "bold"),
                    bg="#673AB7", fg="white", relief=tk.RAISED, bd=1).grid(row=0, column=col, sticky="nsew", padx=1, pady=1)
            pair_table_frame.columnconfigure(col, weight=1)
        
        # 组合名称映射
        pair_name_map = {
            '千百': '千位-百位', '千十': '千位-十位', '千个': '千位-个位',
            '百十': '百位-十位', '百个': '百位-个位',
            '十个': '十位-个位'
        }
        
        # 填充两两组合数据
        row = 1
        for pair_key, stats in pair_stats.items():
            hit_count = stats['hit_count']
            total_count = stats['total_count']
            hit_rate = hit_count / total_count if total_count > 0 else 0
            
            # 交替行背景色
            row_bg = "#F8F9FA" if row % 2 == 0 else "white"
            
            # 组合名称
            display_name = pair_name_map.get(pair_key, pair_key)
            tk.Label(pair_table_frame, text=display_name, font=("SimHei", 9),
                    bg=row_bg, relief=tk.SUNKEN, bd=1, fg="#333").grid(row=row, column=0, sticky="nsew", padx=1, pady=1)
            
            # 命中次数
            tk.Label(pair_table_frame, text=str(hit_count), font=("SimHei", 9),
                    bg=row_bg, relief=tk.SUNKEN, bd=1, fg="#1976D2").grid(row=row, column=1, sticky="nsew", padx=1, pady=1)
            
            # 总次数
            tk.Label(pair_table_frame, text=str(total_count), font=("SimHei", 9),
                    bg=row_bg, relief=tk.SUNKEN, bd=1, fg="#666").grid(row=row, column=2, sticky="nsew", padx=1, pady=1)
            
            # 命中率
            rate_text = f"{hit_rate*100:.1f}%"
            rate_color = "#90EE90" if hit_rate >= 0.7 else "#FFE082" if hit_rate >= 0.5 else "#FFCCCC"
            tk.Label(pair_table_frame, text=rate_text, font=("SimHei", 9),
                    bg=rate_color, relief=tk.SUNKEN, bd=1, fg="#333").grid(row=row, column=3, sticky="nsew", padx=1, pady=1)
            
            # 状态
            if hit_rate >= 0.7:
                status = "优秀"
                status_color = "#4CAF50"
            elif hit_rate >= 0.5:
                status = "良好"
                status_color = "#FF9800"
            else:
                status = "一般"
                status_color = "#f44336"
            
            tk.Label(pair_table_frame, text=status, font=("SimHei", 9),
                    bg=row_bg, fg=status_color, relief=tk.SUNKEN, bd=1).grid(row=row, column=4, sticky="nsew", padx=1, pady=1)
            
            row += 1
        
        # 设置列权重
        for col in range(5):
            pair_table_frame.grid_columnconfigure(col, weight=1)
        
        # 添加分隔线
        separator = tk.Frame(backtest_content_frame, height=2, bg="#E0E0E0")
        separator.pack(fill=tk.X, pady=8)
        
        # 创建详细结果表格
        detail_title_frame = tk.Frame(backtest_content_frame, bg="#607D8B", height=25)
        detail_title_frame.pack(fill=tk.X)
        detail_title_frame.pack_propagate(False)
        
        tk.Label(detail_title_frame, text="📋 详细回测结果", font=("SimHei", 10, "bold"), 
                bg="#607D8B", fg="white").pack(side=tk.LEFT, padx=10, pady=3)
        
        table_frame = tk.Frame(backtest_content_frame, bg="white", padx=10, pady=5)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建表头
        headers = ["期号", "实际开奖", "千位预测", "百位预测", "十位预测", "个位预测", "命中率", "4位全中", "千百", "千十", "千个", "百十", "百个", "十个"]
        column_widths = [6, 10, 9, 9, 9, 9, 8, 8, 6, 6, 6, 6, 6, 6]  # 调整每列宽度
        for col, (name, width) in enumerate(zip(headers, column_widths)):
            header = tk.Label(table_frame, text=name, font=('SimHei', 10, 'bold'),
                             bg='#e0e0e0', relief=tk.RAISED, bd=1, width=width)
            header.grid(row=0, column=col, sticky='nsew', padx=1, pady=1)
        
        # 设置列最小宽度而非权重
        for col, width in enumerate(column_widths):
            table_frame.grid_columnconfigure(col, minsize=width*10)  # 每个字符约10像素
        
        # 填充表格数据
        position_names = ['千位', '百位', '十位', '个位', '球五']
        
        for row, result in enumerate(results):
            # 期号
            tk.Label(table_frame, text=str(result['period']), font=("SimHei", 9),
                    bg="white", relief=tk.SUNKEN, bd=1).grid(row=row+1, column=0, sticky="nsew")
            
            # 实际开奖
            actual_text = " ".join(map(str, result['actual']))
            tk.Label(table_frame, text=actual_text, font=("SimHei", 9),
                    bg="white", relief=tk.SUNKEN, bd=1).grid(row=row+1, column=1, sticky="nsew")
            
            # 各位置预测
            for pos in range(4):  # 只显示前4位
                predicted_nums = sorted(result['predictions'][pos])
                predicted_text = ", ".join(map(str, predicted_nums))
                bg_color = "#90EE90" if result['position_hits'][pos] else "#FFCCCC"
                tk.Label(table_frame, text=predicted_text, font=("SimHei", 8),
                        bg=bg_color, relief=tk.SUNKEN, bd=1).grid(row=row+1, column=pos+2, sticky="nsew")
            
            # 命中率
            hit_rate_text = f"{result['hit_rate']*100:.1f}%"
            tk.Label(table_frame, text=hit_rate_text, font=("SimHei", 9),
                    bg="white", relief=tk.SUNKEN, bd=1).grid(row=row+1, column=6, sticky="nsew")
            
            # 4位全中
            full_hit_text = "是" if all(result['position_hits']) else "否"
            bg_color = "#90EE90" if all(result['position_hits']) else "#FFCCCC"
            tk.Label(table_frame, text=full_hit_text, font=("SimHei", 9),
                    bg=bg_color, relief=tk.SUNKEN, bd=1).grid(row=row+1, column=7, sticky="nsew")
            
            # 两两组合命中情况
            pair_keys = ['千百', '千十', '千个', '百十', '百个', '十个']
            for col_idx, pair_key in enumerate(pair_keys):
                if pair_key in result['pair_hits']:
                    hit_status = "✓" if result['pair_hits'][pair_key] else "✗"
                    bg_color = "#90EE90" if result['pair_hits'][pair_key] else "#FFCCCC"
                else:
                    hit_status = "-"
                    bg_color = "white"
                
                tk.Label(table_frame, text=hit_status, font=("SimHei", 10),
                        bg=bg_color, relief=tk.SUNKEN, bd=1).grid(row=row+1, column=8+col_idx, sticky="nsew")
        
        # 更新Canvas的滚动区域
        self.backtest_canvas.configure(scrollregion=self.backtest_canvas.bbox("all"))
    
    def _update_result_display(self, num_periods):
        # 清除现有结果
        for widget in self.result_frame.winfo_children():
            widget.destroy()
        
        # 预测结果标题
        result_title_frame = tk.Frame(self.result_frame, bg="#FF9800", height=25)
        result_title_frame.pack(fill=tk.X)
        result_title_frame.pack_propagate(False)
        
        tk.Label(result_title_frame, text="🎯 智能预测结果", font=("SimHei", 10, "bold"), 
                bg="#FF9800", fg="white").pack(side=tk.LEFT, padx=10, pady=3)
        
        # 预测结果内容框架
        result_content_frame = tk.Frame(self.result_frame, bg="white", padx=10, pady=8)
        result_content_frame.pack(fill=tk.BOTH, expand=True)
        
        # 显示当前使用的参数（每个参数换行显示）
        params = self.get_current_params()
        key_params = {k: v for k, v in params.items() if k in ['lambda', 'co_weight', 'digit_transition_weight', 'sum_transition_weight', 'digit_exclusion_weight']}
        
        # 创建参数显示框架
        params_frame = tk.Frame(result_content_frame, bg="#F5F5F5", relief=tk.SUNKEN, bd=1)
        params_frame.pack(fill=tk.X, pady=3, padx=8)
        
        # 参数标题
        tk.Label(params_frame, text="模型参数:", font=("SimHei", 9, "bold"), 
                bg="#F5F5F5", fg="#333", pady=2).pack(anchor=tk.W, padx=8)
        
        # 每个参数单独一行显示
        for param_name, param_value in key_params.items():
            param_text = f"  {param_name} = {param_value:.3f}"
            tk.Label(params_frame, text=param_text, font=("SimHei", 9), 
                    bg="#F5F5F5", fg="#666", pady=1).pack(anchor=tk.W, padx=8)
        
        # 计算预测期号
        if self.last_period is not None:
            # 尝试解析期号格式
            try:
                if isinstance(self.last_period, str):
                    # 处理类似 "2023001" 或 "第2023001期" 格式
                    import re
                    match = re.search(r'\d+', self.last_period)
                    if match:
                        number_part = match.group(0)
                        prefix = self.last_period[:self.last_period.index(number_part)]
                        suffix = self.last_period[self.last_period.index(number_part)+len(number_part):]
                        next_number = int(number_part) + 1
                        next_period = f"{prefix}{next_number}{suffix}"
                    else:
                        # 无法解析，直接加1
                        next_period = str(int(self.last_period) + 1)
                else:
                    next_period = self.last_period + 1
            except:
                next_period = f"{self.last_period + 1}"
            
            # 显示预测期号
            period_label = tk.Label(result_content_frame, text=f"📅 预测期号: {next_period}",
                                  font=("SimHei", 12, "bold"), bg="white", pady=5, fg="#E65100")
            period_label.pack()
        
        # 显示基本信息
        info_label = tk.Label(result_content_frame, text=f"📊 已分析 {num_periods} 期历史数据",
                             font=("SimHei", 10), bg="white", pady=3, fg="#666")
        info_label.pack()
        
        # 创建结果表格
        position_names = ['千位', '百位', '十位', '个位', '球五']
        
        # 创建表格框架
        table_frame = tk.Frame(result_content_frame, bg="white")
        table_frame.pack(fill=tk.X, pady=5)
        
        # 创建表头
        headers = ["位置", "推荐数字", "操作"]
        for col, name in enumerate(headers):
            header = tk.Label(table_frame, text=name, font=("SimHei", 9, "bold"),
                             bg="#2196F3", fg="white", relief=tk.RAISED, bd=1)
            header.grid(row=0, column=col, sticky="nsew", padx=1, pady=1)
        
        # 设置列权重，使其均匀分布
        for col in range(3):
            table_frame.grid_columnconfigure(col, weight=1)
        
        # 创建复制函数工厂
        def create_copy_function(numbers_to_copy):
            def copy_numbers():
                self.root.clipboard_clear()
                self.root.clipboard_append(numbers_to_copy)
                self.root.update()
            return copy_numbers
        
        # 填充表格数据
        for row, pos in enumerate(range(5)):
            # 交替行背景色
            row_bg = "#F8F9FA" if row % 2 == 0 else "white"
            
            # 位置名称
            pos_label = tk.Label(table_frame, text=position_names[pos], font=("SimHei", 9),
                                bg=row_bg, relief=tk.SUNKEN, bd=1, fg="#333")
            pos_label.grid(row=row+1, column=0, sticky="nsew", padx=1, pady=1)
            
            # 推荐数字
            nums = sorted(self.predictions[pos])
            nums_text = "".join(map(str, nums))
            nums_label = tk.Label(table_frame, text=nums_text, font=("SimHei", 9),
                                 bg=row_bg, relief=tk.SUNKEN, bd=1, fg="#1976D2")
            nums_label.grid(row=row+1, column=1, sticky="nsew", padx=1, pady=1)
            
            # 复制按钮 - 使用工厂函数避免闭包问题
            copy_btn = tk.Button(table_frame, text="复制", font=("SimHei", 8), 
                                bg="#4CAF50", fg="white", relief=tk.RAISED, bd=1,
                                command=create_copy_function(nums_text), padx=8, pady=2)
            copy_btn.grid(row=row+1, column=2, sticky="nsew", padx=1, pady=1)
        
        # 添加复制所有数据按钮
        def copy_all_data():
            all_data = []
            position_names = ['千位', '百位', '十位', '个位', '球五']
            for pos in range(5):
                nums = sorted(self.predictions[pos])
                nums_text = "".join(map(str, nums))
                all_data.append(f"{position_names[pos]}：{nums_text}")
            
            # 将所有数据用换行符连接
            full_text = "\n".join(all_data)
            self.root.clipboard_clear()
            self.root.clipboard_append(full_text)
            self.root.update()
        
        # 创建复制所有数据按钮框架
        copy_all_frame = tk.Frame(result_content_frame, bg="white")
        copy_all_frame.pack(fill=tk.X, pady=5)
        
        copy_all_btn = tk.Button(copy_all_frame, text="复制所有数据", font=("SimHei", 10, "bold"), 
                                bg="#FF5722", fg="white", relief=tk.RAISED, bd=2,
                                command=copy_all_data, padx=20, pady=6)
        copy_all_btn.pack()
        
        # 添加投资收益统计显示
        # 创建投资收益统计框架
        investment_stats_frame = tk.Frame(result_content_frame, bg="white", relief=tk.RAISED, bd=1)
        investment_stats_frame.pack(fill=tk.X, pady=10)
        
        # 投资收益统计标题
        investment_title_frame = tk.Frame(investment_stats_frame, bg="#9C27B0", height=25)
        investment_title_frame.pack(fill=tk.X)
        investment_title_frame.pack_propagate(False)
        
        tk.Label(investment_title_frame, text="💰 投资收益统计", font=("SimHei", 10, "bold"), 
                bg="#9C27B0", fg="white").pack(side=tk.LEFT, padx=10, pady=3)
        
        # 投资收益统计内容
        investment_content_frame = tk.Frame(investment_stats_frame, bg="white", padx=10, pady=8)
        investment_content_frame.pack(fill=tk.X)
        
        # 投注倍数输入框
        bet_multiplier_frame = tk.Frame(investment_content_frame, bg="white")
        bet_multiplier_frame.pack(fill=tk.X, pady=5)
        
        tk.Label(bet_multiplier_frame, text="投注倍数:", font=("SimHei", 9), bg="white").pack(side=tk.LEFT)
        bet_multiplier_var = tk.StringVar(value="0.2")
        bet_multiplier_entry = tk.Entry(bet_multiplier_frame, textvariable=bet_multiplier_var, width=8)
        bet_multiplier_entry.pack(side=tk.LEFT, padx=5)
        
        # 计算投资收益统计
        def calculate_investment_stats():
            try:
                bet_multiplier = float(bet_multiplier_var.get())
                if bet_multiplier <= 0:
                    messagebox.showwarning("警告", "投注倍数必须大于0")
                    return
            except ValueError:
                messagebox.showwarning("警告", "请输入有效的投注倍数")
                return
            
            # 获取回测数据
            # 从回测设置中获取回测期数
            try:
                total_periods = int(self.backtest_periods_var.get())
            except:
                total_periods = 30  # 默认值
            
            # 从回测结果中获取全中次数
            # 使用实际的回测结果来计算
            if hasattr(self, 'backtest_results') and self.backtest_results:
                # 计算实际的全中次数 (hit_rate == 1.0 表示100%命中)
                full_hits = sum(1 for result in self.backtest_results if result['hit_rate'] == 1.0)
                full_hit_rate = full_hits / total_periods if total_periods > 0 else 0
            else:
                # 如果没有回测结果，使用默认值
                full_hit_rate = 0.70  # 默认70%的4位全中率
                full_hits = int(total_periods * full_hit_rate)
            
            non_full_hits = total_periods - full_hits
            
            # 计算各项指标
            cost_per_period = 8 * 8 * 8 * 8 * bet_multiplier  # 每期成本
            total_cost = total_periods * cost_per_period  # 总成本
            single_win_amount = 9300 * bet_multiplier  # 单次中奖金额
            total_win_amount = full_hits * single_win_amount  # 总中奖金额
            total_profit = total_win_amount - total_cost  # 总利润
            win_rate = (full_hits / total_periods) * 100  # 胜率
            profit_margin = (total_profit / total_cost) * 100 if total_cost > 0 else 0  # 利润率
            
            # 清除现有统计显示
            try:
                for widget in self.stats_display_frame.winfo_children():
                    widget.destroy()
            except:
                return
            
            # 创建统计表格
            stats_table_frame = tk.Frame(self.stats_display_frame, bg="white")
            stats_table_frame.pack(fill=tk.X, pady=5)
            
            # 表格标题
            headers = ["项目", "数值"]
            for col, header in enumerate(headers):
                header_label = tk.Label(stats_table_frame, text=header, font=("SimHei", 9, "bold"),
                                       bg="#E0E0E0", relief=tk.RAISED, bd=1)
                header_label.grid(row=0, column=col, sticky="nsew", padx=1, pady=1)
            
            # 统计数据
            stats_data = [
                ("回测期数", f"{total_periods}期"),
                ("全中次数", f"{full_hits}次"),
                ("非全中次数", f"{non_full_hits}次"),
                ("投注倍数", f"{bet_multiplier}倍"),
                ("每期成本", f"{cost_per_period:.2f}元"),
                ("总成本", f"{total_cost:.2f}元"),
                ("单次中奖金额", f"{single_win_amount:.2f}元"),
                ("总中奖金额", f"{total_win_amount:.2f}元"),
                ("总利润", f"{total_profit:.2f}元"),
                ("胜率", f"{win_rate:.2f}%"),
                ("利润率", f"{profit_margin:.2f}%")
            ]
            
            for i, (item, value) in enumerate(stats_data, 1):
                # 项目名称
                item_label = tk.Label(stats_table_frame, text=item, font=("SimHei", 9),
                                     bg="white", relief=tk.SUNKEN, bd=1)
                item_label.grid(row=i, column=0, sticky="nsew", padx=1, pady=1)
                
                # 数值
                value_color = "#4CAF50" if item in ["胜率", "利润率"] else "black"
                value_label = tk.Label(stats_table_frame, text=value, font=("SimHei", 9),
                                      bg="white", fg=value_color, relief=tk.SUNKEN, bd=1)
                value_label.grid(row=i, column=1, sticky="nsew", padx=1, pady=1)
            
            # 设置列权重
            stats_table_frame.grid_columnconfigure(0, weight=1)
            stats_table_frame.grid_columnconfigure(1, weight=1)
            
            # 投资建议
            if profit_margin > 0:
                recommendation = "投资建议: 策略盈利, 建议继续使用"
                recommendation_color = "#4CAF50"
            else:
                recommendation = "投资建议: 策略亏损, 建议调整参数"
                recommendation_color = "#F44336"
            
            recommendation_label = tk.Label(self.stats_display_frame, text=recommendation,
                                          font=("SimHei", 10, "bold"), bg="white", fg=recommendation_color)
            recommendation_label.pack(pady=5)
        
        # 计算按钮
        calculate_btn = tk.Button(bet_multiplier_frame, text="计算", font=("SimHei", 9),
                                 bg="#2196F3", fg="white", relief=tk.RAISED, bd=1,
                                 command=calculate_investment_stats, padx=10, pady=2)
        calculate_btn.pack(side=tk.LEFT, padx=10)
        
        # 统计结果显示框架
        self.stats_display_frame = tk.Frame(investment_content_frame, bg="white")
        self.stats_display_frame.pack(fill=tk.X, pady=5)
        
        # 页面加载时自动计算投资收益统计
        def auto_calculate_investment_stats():
            try:
                bet_multiplier = float(bet_multiplier_var.get())
                if bet_multiplier <= 0:
                    return
            except ValueError:
                return
            
            # 检查stats_display_frame是否还存在
            try:
                if not hasattr(self, 'stats_display_frame') or not self.stats_display_frame.winfo_exists():
                    return
            except:
                return
            
            # 获取回测数据
            try:
                total_periods = int(self.backtest_periods_var.get())
            except:
                total_periods = 30  # 默认值
            
            # 从回测结果中获取全中次数
            if hasattr(self, 'backtest_results') and self.backtest_results:
                # 计算实际的全中次数 (hit_rate == 1.0 表示100%命中)
                full_hits = sum(1 for result in self.backtest_results if result['hit_rate'] == 1.0)
                full_hit_rate = full_hits / total_periods if total_periods > 0 else 0
            else:
                # 如果没有回测结果，使用默认值
                full_hit_rate = 0.70  # 默认70%的4位全中率
                full_hits = int(total_periods * full_hit_rate)
            
            non_full_hits = total_periods - full_hits
            
            # 计算各项指标
            cost_per_period = 8 * 8 * 8 * 8 * bet_multiplier  # 每期成本
            total_cost = total_periods * cost_per_period  # 总成本
            single_win_amount = 9300 * bet_multiplier  # 单次中奖金额
            total_win_amount = full_hits * single_win_amount  # 总中奖金额
            total_profit = total_win_amount - total_cost  # 总利润
            win_rate = (full_hits / total_periods) * 100  # 胜率
            profit_margin = (total_profit / total_cost) * 100 if total_cost > 0 else 0  # 利润率
            
            # 清除现有统计显示
            try:
                for widget in self.stats_display_frame.winfo_children():
                    widget.destroy()
            except:
                return
            
            # 创建统计表格
            stats_table_frame = tk.Frame(self.stats_display_frame, bg="white")
            stats_table_frame.pack(fill=tk.X, pady=5)
            
            # 表格标题
            headers = ["项目", "数值"]
            for col, header in enumerate(headers):
                header_label = tk.Label(stats_table_frame, text=header, font=("SimHei", 9, "bold"),
                                       bg="#E0E0E0", relief=tk.RAISED, bd=1)
                header_label.grid(row=0, column=col, sticky="nsew", padx=1, pady=1)
            
            # 统计数据
            stats_data = [
                ("回测期数", f"{total_periods}期"),
                ("全中次数", f"{full_hits}次"),
                ("非全中次数", f"{non_full_hits}次"),
                ("投注倍数", f"{bet_multiplier}倍"),
                ("每期成本", f"{cost_per_period:.2f}元"),
                ("总成本", f"{total_cost:.2f}元"),
                ("单次中奖金额", f"{single_win_amount:.2f}元"),
                ("总中奖金额", f"{total_win_amount:.2f}元"),
                ("总利润", f"{total_profit:.2f}元"),
                ("胜率", f"{win_rate:.2f}%"),
                ("利润率", f"{profit_margin:.2f}%")
            ]
            
            for i, (item, value) in enumerate(stats_data, 1):
                # 项目名称
                item_label = tk.Label(stats_table_frame, text=item, font=("SimHei", 9),
                                     bg="white", relief=tk.SUNKEN, bd=1)
                item_label.grid(row=i, column=0, sticky="nsew", padx=1, pady=1)
                
                # 数值
                value_color = "#4CAF50" if item in ["胜率", "利润率"] else "black"
                value_label = tk.Label(stats_table_frame, text=value, font=("SimHei", 9),
                                      bg="white", fg=value_color, relief=tk.SUNKEN, bd=1)
                value_label.grid(row=i, column=1, sticky="nsew", padx=1, pady=1)
            
            # 设置列权重
            stats_table_frame.grid_columnconfigure(0, weight=1)
            stats_table_frame.grid_columnconfigure(1, weight=1)
            
            # 投资建议
            if profit_margin > 0:
                recommendation = "投资建议: 策略盈利, 建议继续使用"
                recommendation_color = "#4CAF50"
            else:
                recommendation = "投资建议: 策略亏损, 建议调整参数"
                recommendation_color = "#F44336"
            
            recommendation_label = tk.Label(self.stats_display_frame, text=recommendation,
                                          font=("SimHei", 10, "bold"), bg="white", fg=recommendation_color)
            recommendation_label.pack(pady=5)
        
        # 页面加载时自动计算
        self.root.after(100, auto_calculate_investment_stats)
    

    
    def show_params_dialog(self):
        """显示参数设置对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("模型参数设置")
        dialog.geometry("500x600")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 创建参数输入框
        param_entries = {}
        param_labels = {
            'alpha': '平滑因子 (alpha):',
            'lambda': '冷号衰减系数 (lambda):',
            'co_weight': '协同预测权重:',
            'digit_transition_weight': '数字转移权重:',
            'sum_transition_weight': '数字和转移权重:',
            'digit_exclusion_weight': '数字排除权重:',
            'overall_weight': '全中率权重:',
            'hot_threshold': '热号阈值:',
            'cold_threshold': '冷号阈值:',
            'selection_count': '选择数量:',
            'window': '窗口大小:',
            'periodicity': '周期特征期数:',
            'hot_multiplier': '热号权重系数:',
            'cold_multiplier': '冷号权重系数:'
        }
        
        # 获取当前参数值
        current_params = self.get_current_params()
        
        frame = tk.Frame(dialog, padx=20, pady=10)
        frame.pack(fill=tk.BOTH, expand=True)
        
        for i, (param, label_text) in enumerate(param_labels.items()):
            tk.Label(frame, text=label_text, font=("SimHei", 10)).grid(row=i, column=0, sticky=tk.W, pady=5)
            
            if param in ['selection_count', 'window', 'periodicity']:
                var = tk.IntVar(value=int(current_params[param]))
            else:
                var = tk.DoubleVar(value=current_params[param])
            entry = tk.Entry(frame, textvariable=var, width=10)
            entry.grid(row=i, column=1, sticky=tk.W, pady=5)
            
            param_entries[param] = var
        
        # 添加说明文本
        help_text = "提示:\n" \
                   "- 协同预测、数字转移、数字和转移、数字排除权重之和应为1\n" \
                   "- 调整参数后需重新回测和预测\n" \
                   "- 参数将自动保存到配置文件"
        tk.Label(frame, text=help_text, font=("SimHei", 9), fg="blue", justify=tk.LEFT).grid(
            row=len(param_labels), column=0, columnspan=2, sticky=tk.W, pady=10)
        
        # 按钮框架
        btn_frame = tk.Frame(dialog)
        btn_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 确定按钮
        def save_params():
            new_params = {}
            for param, var in param_entries.items():
                try:
                    if param == 'selection_count':
                        value = int(var.get())
                    else:
                        value = float(var.get())
                    new_params[param] = value
                except ValueError:
                    messagebox.showerror("参数错误", f"{param_labels[param]}必须是数字")
                    return
            
            # 验证权重之和是否接近1
            weights = [new_params['co_weight'], new_params['digit_transition_weight'], new_params['sum_transition_weight'], new_params['digit_exclusion_weight']]
            if abs(sum(weights) - 1.0) > 0.01:
                messagebox.showwarning("权重警告", "四种预测方法的权重之和应接近1.0")
            
            # 保存参数到配置文件（保持last_excel_file不变）
            self.config.update(new_params)
            if save_config(self.config):
                # 更新当前参数
                self.default_params = new_params
                messagebox.showinfo("参数设置", "参数已保存到配置文件，下次预测将使用新参数")
                dialog.destroy()
            else:
                messagebox.showerror("保存失败", "参数保存到配置文件失败")
        
        tk.Button(btn_frame, text="确定", command=save_params, bg="#4CAF50", fg="white", 
                 padx=15).pack(side=tk.LEFT, padx=5)
        
        # 重置按钮
        def reset_params():
            for param, var in param_entries.items():
                var.set(DEFAULT_CONFIG[param])
        
        tk.Button(btn_frame, text="重置默认", command=reset_params, bg="#FF9800", fg="white", 
                 padx=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(btn_frame, text="取消", command=dialog.destroy, bg="#f44336", fg="white", 
                 padx=15).pack(side=tk.LEFT, padx=5)
    
    def get_current_params(self):
        """获取当前使用的参数"""
        return self.default_params.copy()
    
    def auto_optimize_params(self):
        """自动优化参数"""
        if self.history_data is None:
            messagebox.showwarning("警告", "请先加载数据")
            return
            
        # 创建参数优化对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("参数自动优化")
        dialog.geometry("800x600")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 创建主框架
        main_frame = tk.Frame(dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建左右分栏
        left_frame = tk.Frame(main_frame, relief=tk.GROOVE, bd=2)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        right_frame = tk.Frame(main_frame, relief=tk.GROOVE, bd=2)
        right_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧：参数设置
        title_frame = tk.Frame(left_frame, bg="#4CAF50")
        title_frame.pack(fill=tk.X)
        tk.Label(title_frame, text="优化设置", font=("SimHei", 12, "bold"), 
                bg="#4CAF50", fg="white", pady=5).pack()
        
        # 参数设置区域
        params_frame = tk.Frame(left_frame, padx=20, pady=10)
        params_frame.pack(fill=tk.BOTH)
        
        # 回测期数
        param_frame1 = tk.Frame(params_frame)
        param_frame1.pack(fill=tk.X, pady=5)
        tk.Label(param_frame1, text="回测期数:", font=("SimHei", 10)).pack(side=tk.LEFT)
        backtest_periods_var = tk.IntVar(value=30)
        backtest_periods_entry = tk.Entry(param_frame1, textvariable=backtest_periods_var, width=10)
        backtest_periods_entry.pack(side=tk.LEFT, padx=5)
        
        # 回撤天数
        param_frame1_2 = tk.Frame(params_frame)
        param_frame1_2.pack(fill=tk.X, pady=5)
        tk.Label(param_frame1_2, text="回撤天数:", font=("SimHei", 10)).pack(side=tk.LEFT)
        drawdown_days_var = tk.IntVar(value=0)
        drawdown_days_entry = tk.Entry(param_frame1_2, textvariable=drawdown_days_var, width=10)
        drawdown_days_entry.pack(side=tk.LEFT, padx=5)
        
        # 目标命中率
        param_frame2 = tk.Frame(params_frame)
        param_frame2.pack(fill=tk.X, pady=5)
        tk.Label(param_frame2, text="目标命中率(%):", font=("SimHei", 10)).pack(side=tk.LEFT)
        target_hit_rate_var = tk.DoubleVar(value=85.0)
        target_hit_rate_entry = tk.Entry(param_frame2, textvariable=target_hit_rate_var, width=10)
        target_hit_rate_entry.pack(side=tk.LEFT, padx=5)
        
        # 最大迭代次数
        param_frame3 = tk.Frame(params_frame)
        param_frame3.pack(fill=tk.X, pady=5)
        tk.Label(param_frame3, text="最大迭代次数:", font=("SimHei", 10)).pack(side=tk.LEFT)
        max_iterations_var = tk.IntVar(value=100)
        max_iterations_entry = tk.Entry(param_frame3, textvariable=max_iterations_var, width=10)
        max_iterations_entry.pack(side=tk.LEFT, padx=5)
        
        # 种群大小
        param_frame4 = tk.Frame(params_frame)
        param_frame4.pack(fill=tk.X, pady=5)
        tk.Label(param_frame4, text="种群大小:", font=("SimHei", 10)).pack(side=tk.LEFT)
        population_size_var = tk.IntVar(value=50)
        population_size_entry = tk.Entry(param_frame4, textvariable=population_size_var, width=10)
        population_size_entry.pack(side=tk.LEFT, padx=5)
        
        # 线程数
        param_frame5 = tk.Frame(params_frame)
        param_frame5.pack(fill=tk.X, pady=5)
        tk.Label(param_frame5, text="线程数:", font=("SimHei", 10)).pack(side=tk.LEFT)
        num_threads_var = tk.IntVar(value=4)
        num_threads_entry = tk.Entry(param_frame5, textvariable=num_threads_var, width=10)
        num_threads_entry.pack(side=tk.LEFT, padx=5)
        
        # 进度显示框架
        progress_frame = tk.Frame(left_frame, padx=20, pady=10)
        progress_frame.pack(fill=tk.X)
        
        # 进度条
        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(progress_frame, variable=progress_var, maximum=100, mode='determinate')
        progress_bar.pack(fill=tk.X, pady=5)
        
        # 当前迭代信息
        iteration_var = tk.StringVar(value="准备优化...")
        iteration_label = tk.Label(progress_frame, textvariable=iteration_var, font=("SimHei", 10))
        iteration_label.pack(pady=5)
        
        # 当前最佳结果
        best_result_var = tk.StringVar(value="当前最佳命中率: 0.00%")
        best_result_label = tk.Label(progress_frame, textvariable=best_result_var, font=("SimHei", 10))
        best_result_label.pack(pady=5)
        
        # 右侧：优化日志
        title_frame2 = tk.Frame(right_frame, bg="#2196F3")
        title_frame2.pack(fill=tk.X)
        tk.Label(title_frame2, text="优化日志", font=("SimHei", 12, "bold"), 
                bg="#2196F3", fg="white", pady=5).pack()
        
        # 创建日志文本框和滚动条
        log_frame = tk.Frame(right_frame, padx=10, pady=10)
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        log_scrollbar = tk.Scrollbar(log_frame)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        log_text = tk.Text(log_frame, height=20, width=40, font=("SimHei", 9),
                          yscrollcommand=log_scrollbar.set, wrap=tk.WORD,
                          bg="#f5f5f5", relief=tk.SUNKEN)
        log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.config(command=log_text.yview)
        
        # 添加日志函数
        def add_log(message):
            dialog.after(0, lambda: log_text.insert(tk.END, f"{message}\n"))
            dialog.after(0, lambda: log_text.see(tk.END))
            dialog.after(0, log_text.update)
        
        # 按钮框架
        btn_frame = tk.Frame(left_frame)
        btn_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 优化状态变量
        optimization_running = False
        current_thread = None
        current_optimizer = None
        
        def stop_optimization():
            """停止优化进程"""
            nonlocal optimization_running
            optimization_running = False
            add_log("正在停止优化...")
            stop_button.config(state='disabled')
            start_button.config(state='normal')
            if current_optimizer and current_optimizer.best_params:
                apply_button.config(state='normal')
            add_log("优化已停止")
        
        def close_dialog():
            """关闭对话框"""
            stop_optimization()
            if current_thread in self.running_threads:
                self.running_threads.remove(current_thread)
            dialog.destroy()
        
        def apply_best_params():
            """应用最佳参数"""
            if current_optimizer and current_optimizer.best_params:
                # 更新配置
                self.config.update(current_optimizer.best_params)
                if save_config(self.config):
                    self.default_params = current_optimizer.best_params
                    messagebox.showinfo("成功", "已应用最佳参数")
                else:
                    messagebox.showerror("错误", "保存参数失败")
        
        # 优化进度回调函数
        def progress_callback(iteration, total, current_best_rate, current_params):
            if not optimization_running:
                return False
            
            try:
                # 更新进度条
                progress = (iteration / total) * 100
                dialog.after(0, lambda: progress_var.set(progress))
                
                # 更新迭代信息
                status_text = f"迭代进度: {iteration}/{total} ({progress:.1f}%)"
                dialog.after(0, lambda: iteration_var.set(status_text))
                
                # 更新最佳结果
                result_text = f"当前最佳命中率: {current_best_rate*100:.2f}%"
                dialog.after(0, lambda: best_result_var.set(result_text))
                
                # 记录日志 - 修复条件判断
                should_log = False
                if iteration <= 10:  # 前10次迭代都记录
                    should_log = True
                elif iteration % 10 == 0:  # 之后每10次记录一次
                    should_log = True
                elif hasattr(current_optimizer, 'best_hit_rate') and current_best_rate > current_optimizer.best_hit_rate:  # 有改进时记录
                    should_log = True
                
                if should_log:
                    log_message = f"迭代 {iteration}:\n"
                    log_message += f"最佳命中率: {current_best_rate*100:.2f}%\n"
                    log_message += "当前最佳参数:\n"
                    for k, v in current_params.items():
                        log_message += f"  {k}: {v:.3f}\n"
                    log_message += "-" * 30 + "\n"
                    dialog.after(0, lambda msg=log_message: add_log(msg))
                
                # 强制更新UI
                dialog.after(0, lambda: dialog.update_idletasks())
                return True
            except Exception as e:
                print(f"更新进度时发生错误: {e}")
                return True  # 继续优化
        
        def run_optimization():
            """运行优化进程"""
            nonlocal optimization_running, current_thread, current_optimizer
            try:
                backtest_periods = backtest_periods_var.get()
                drawdown_days = drawdown_days_var.get()
                target_hit_rate = target_hit_rate_var.get() / 100.0
                max_iterations = max_iterations_var.get()
                population_size = population_size_var.get()
                num_threads = num_threads_var.get()
                
                # 验证参数
                if backtest_periods <= 0:
                    messagebox.showwarning("警告", "回测期数必须大于0")
                    return
                
                if drawdown_days < 0:
                    messagebox.showwarning("警告", "回撤天数不能为负数")
                    return
                
                # 计算实际可用的数据量
                available_data = len(self.history_data) - drawdown_days
                if available_data <= 0:
                    messagebox.showwarning("警告", f"回撤天数({drawdown_days})不能大于等于总数据量({len(self.history_data)})")
                    return
                
                if backtest_periods >= available_data:
                    messagebox.showwarning("警告", f"回测期数({backtest_periods})不能大于等于可用数据量({available_data})")
                    return
                
                if target_hit_rate <= 0 or target_hit_rate > 1.0:
                    messagebox.showwarning("警告", "目标命中率必须在1-100%之间")
                    return
                
                if max_iterations <= 0:
                    messagebox.showwarning("警告", "最大迭代次数必须大于0")
                    return
                
                if population_size < 10:
                    messagebox.showwarning("警告", "种群大小必须至少为10")
                    return
                    
                if num_threads < 1:
                    messagebox.showwarning("警告", "线程数必须大于0")
                    return
                
                # 清空日志
                log_text.delete(1.0, tk.END)
                add_log("正在初始化优化过程...")
                add_log(f"使用 {num_threads} 个线程进行并行计算")
                
                # 禁用输入和开始按钮
                for widget in [backtest_periods_entry, drawdown_days_entry, target_hit_rate_entry, max_iterations_entry, 
                             population_size_entry, num_threads_entry, start_button]:
                    widget.config(state='disabled')
                
                # 启用停止按钮
                stop_button.config(state='normal')
                
                # 重置进度显示
                progress_var.set(0)
                iteration_var.set("准备开始优化...")
                best_result_var.set("当前最佳命中率: 0.00%")
                
                optimization_running = True
                
                # 创建优化器
                current_optimizer = ParameterOptimizer(
                    data_file="F:/pl5/data.xlsx",
                    target_hit_rate=target_hit_rate,
                    max_iterations=max_iterations,
                    population_size=population_size,
                    num_threads=num_threads
                )
                
                # 设置回调函数
                current_optimizer.set_progress_callback(progress_callback)
                
                # 定义评估函数
                def evaluate_params(params):
                    if not optimization_running:
                        return 0
                    
                    # 应用回撤天数，忽略最新的数据
                    if drawdown_days > 0:
                        available_data = self.history_data[:-drawdown_days]
                        available_periods = self.periods_data[:-drawdown_days]
                    else:
                        available_data = self.history_data
                        available_periods = self.periods_data
                    
                    # 对最近backtest_periods期进行回测
                    total_periods = len(available_data)
                    total_hits = 0
                    
                    # 定义两两组合（只计算前4位的组合）
                    position_pairs = [
                        (0, 1), (0, 2), (0, 3), (1, 2), (1, 3), (2, 3)  # 千百，千十，千个，百十，百个，十个
                    ]
                    
                    pair_hit_counts = [0] * len(position_pairs)
                    
                    for i in range(total_periods - backtest_periods, total_periods):
                        # 准备训练数据（不包括当前期）
                        train_data = available_data[:i]

                        # 使用当前参数初始化模型
                        # 归一化权重参数以确保总和为1.0
                        weight_sum = params.get('co_weight', 0) + params.get('digit_transition_weight', 0) + params.get('sum_transition_weight', 0) + params.get('digit_exclusion_weight', 0)
                        if weight_sum > 0:
                            params['co_weight'] /= weight_sum
                            params['digit_transition_weight'] /= weight_sum
                            params['sum_transition_weight'] /= weight_sum
                            params['digit_exclusion_weight'] /= weight_sum
                        else:
                            # 默认权重分配
                            params['co_weight'] = 0.25
                            params['digit_transition_weight'] = 0.25
                            params['sum_transition_weight'] = 0.25
                            params['digit_exclusion_weight'] = 0.25

                        model = MFTNModel(params)
                        model.fit(train_data)

                        # 预测 - 使用与正式预测相同的参数
                        predictions = model.predict_next(is_predict=True)

                        # 计算命中情况
                        position_hits = []
                        for pos in range(4):  # 只比较前4位
                            predicted_nums = predictions[pos]
                            actual_num = available_data[i][pos]
                            hit = actual_num in predicted_nums
                            position_hits.append(hit)

                        # 计算两两组合命中情况
                        for pair_idx, (pos1, pos2) in enumerate(position_pairs):
                            if position_hits[pos1] and position_hits[pos2]:
                                pair_hit_counts[pair_idx] += 1

                        # 仅当4球五置都命中时，才计为一次命中
                        if all(position_hits):
                            total_hits += 1
                    
                    # 计算总体命中率
                    overall_hit_rate = total_hits / backtest_periods
                    
                    # 计算两两组合的平均命中率
                    pair_hit_rates = [count / backtest_periods for count in pair_hit_counts]
                    avg_pair_hit_rate = sum(pair_hit_rates) / len(pair_hit_rates)
                    
                    # 仅使用全中率作为评分依据
                    final_score = overall_hit_rate
                    
                    return final_score
                
                # 创建并启动优化线程
                current_thread = threading.Thread(target=lambda: optimize_process(backtest_periods, drawdown_days, current_optimizer, evaluate_params))
                current_thread.daemon = True
                self.running_threads.append(current_thread)
                current_thread.start()
                
            except ValueError:
                messagebox.showwarning("警告", "请输入有效的数值")
        
        def optimize_process(backtest_periods, drawdown_days, optimizer, evaluate_function):
            """优化处理过程"""
            try:
                add_log("开始优化过程...")
                add_log(f"回测期数: {backtest_periods}")
                add_log(f"回撤天数: {drawdown_days}")
                add_log(f"目标命中率: {optimizer.target_hit_rate*100:.1f}%")
                add_log(f"最大迭代次数: {optimizer.max_iterations}")
                add_log(f"使用 {optimizer.num_threads} 个线程进行并行计算")
                add_log("-" * 30)
                
                # 开始优化
                best_params, best_hit_rate = optimizer.optimize(evaluate_function)
                
                if optimization_running and best_params:
                    # 显示优化结果
                    result_text = f"参数优化完成!\n" \
                               f"最佳命中率: {best_hit_rate*100:.2f}%\n\n" \
                               f"最佳参数:\n" + \
                               "\n".join([f"{k} = {v:.3f}" for k, v in best_params.items()])
                    
                    add_log("\n优化完成!")
                    add_log(result_text)
                    
                    # 启用应用最佳参数按钮
                    dialog.after(0, lambda: apply_button.config(state='normal'))
                    
                    dialog.after(0, messagebox.showinfo, "优化完成", result_text)
                
            except Exception as e:
                error_message = f"优化过程中发生错误: {str(e)}"
                add_log(f"\n错误: {error_message}")
                dialog.after(0, messagebox.showerror, "优化错误", error_message)
            finally:
                # 清理线程
                if current_thread in self.running_threads:
                    self.running_threads.remove(current_thread)
                dialog.after(0, lambda: [
                    widget.config(state='normal')
                    for widget in [backtest_periods_entry, drawdown_days_entry, target_hit_rate_entry, max_iterations_entry, 
                                 population_size_entry, num_threads_entry, start_button]
                ])
                dialog.after(0, lambda: stop_button.config(state='disabled'))
        
        # 创建按钮
        start_button = tk.Button(btn_frame, text="开始优化", command=run_optimization,
                               font=("SimHei", 10), bg="#4CAF50", fg="white",
                               relief=tk.RAISED, padx=20)
        start_button.pack(side=tk.LEFT, padx=5)
        
        stop_button = tk.Button(btn_frame, text="停止优化", command=stop_optimization,
                              font=("SimHei", 10), bg="#FF9800", fg="white",
                              relief=tk.RAISED, padx=20, state='disabled')
        stop_button.pack(side=tk.LEFT, padx=5)
        
        apply_button = tk.Button(btn_frame, text="应用最佳参数", command=apply_best_params,
                               font=("SimHei", 10), bg="#2196F3", fg="white",
                               relief=tk.RAISED, padx=20, state='disabled')
        apply_button.pack(side=tk.LEFT, padx=5)
        
        close_button = tk.Button(btn_frame, text="关闭窗口", command=close_dialog,
                               font=("SimHei", 10), bg="#f44336", fg="white",
                               relief=tk.RAISED, padx=20)
        close_button.pack(side=tk.LEFT, padx=5)
        
        # 设置对话框关闭事件
        dialog.protocol("WM_DELETE_WINDOW", close_dialog)

    def show_add_result_dialog(self, edit_period=None):
        """显示添加/修改开奖结果的对话框
        
        Args:
            edit_period: 如果提供，则为修改模式，否则为添加模式
        """
        # 检查F:/pl5/data.xlsx是否存在
        data_file = "F:/pl5/data.xlsx"
        try:
            df = pd.read_excel(data_file)
            if not all(col in df.columns for col in ['期号', '千位', '百位', '十位', '个位', '球五']):
                messagebox.showerror("错误", "F:/pl5/data.xlsx文件格式不正确，请确保包含所有必要的列")
                return
            
            # 确保期号列为字符串类型
            df['期号'] = df['期号'].astype(str)
            
        except Exception as e:
            messagebox.showerror("错误", f"无法读取F:/pl5/data.xlsx文件: {str(e)}")
            return
            
        dialog = tk.Toplevel(self.root)
        dialog.title("修改开奖号码" if edit_period else "录入开奖号码")
        dialog.geometry("400x300")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 创建输入框架
        input_frame = tk.Frame(dialog, padx=20, pady=10)
        input_frame.pack(fill=tk.BOTH, expand=True)
        
        # 期号输入
        period_frame = tk.Frame(input_frame)
        period_frame.pack(fill=tk.X, pady=5)
        tk.Label(period_frame, text="期号:", font=("SimHei", 10)).pack(side=tk.LEFT)
        period_var = tk.StringVar()
        
        if edit_period:
            # 修改模式：设置期号并禁用输入
            period_var.set(edit_period)
            period_entry = tk.Entry(period_frame, textvariable=period_var, width=15, state='readonly')
            # 获取当前号码
            row = df[df['期号'] == str(edit_period)].iloc[0]
            current_numbers = [row['千位'], row['百位'], row['十位'], row['个位'], row['球五']]
        else:
            # 添加模式：计算下一期号
            period_entry = tk.Entry(period_frame, textvariable=period_var, width=15)
            try:
                last_period = str(df['期号'].iloc[-1])
                if last_period:
                    import re
                    match = re.search(r'\d+', last_period)
                    if match:
                        number_part = match.group(0)
                        prefix = last_period[:last_period.index(number_part)]
                        suffix = last_period[last_period.index(number_part)+len(number_part):]
                        next_number = str(int(number_part) + 1).zfill(len(number_part))
                        next_period = f"{prefix}{next_number}{suffix}"
                    else:
                        next_period = str(int(last_period) + 1)
                    period_var.set(next_period)
            except:
                pass
            current_numbers = ['' for _ in range(5)]
            
        period_entry.pack(side=tk.LEFT, padx=5)
        
        # 号码输入框架
        numbers_frame = tk.Frame(input_frame)
        numbers_frame.pack(fill=tk.X, pady=10)
        
        # 创建5个数字输入框
        number_vars = []
        number_entries = []
        position_names = ['千位', '百位', '十位', '个位', '球五']
        
        for i, pos_name in enumerate(position_names):
            pos_frame = tk.Frame(numbers_frame)
            pos_frame.pack(pady=5)
            
            tk.Label(pos_frame, text=f"{pos_name}:", font=("SimHei", 10)).pack(side=tk.LEFT)
            var = tk.StringVar(value=str(current_numbers[i]))
            number_vars.append(var)
            entry = tk.Entry(pos_frame, textvariable=var, width=5)
            entry.pack(side=tk.LEFT, padx=5)
            number_entries.append(entry)
            
            # 添加验证
            def validate_number(P):
                if P == "": return True
                return P.isdigit() and len(P) <= 1 and 0 <= int(P) <= 9
            vcmd = (dialog.register(validate_number), '%P')
            entry.config(validate='key', validatecommand=vcmd)
        
        # 设置焦点移动
        def focus_next(event):
            current = dialog.focus_get()
            if current in number_entries:
                idx = number_entries.index(current)
                if idx < len(number_entries) - 1:
                    number_entries[idx + 1].focus()
                    return "break"
            return None
            
        for entry in number_entries:
            entry.bind('<Key-Return>', focus_next)
            entry.bind('<Key-Tab>', focus_next)
        
        # 保存按钮
        def save_result():
            # 验证输入
            if not period_var.get().strip():
                messagebox.showwarning("警告", "请输入期号")
                return
                
            numbers = []
            for var in number_vars:
                if not var.get().strip():
                    messagebox.showwarning("警告", "请输入完整的开奖号码")
                    return
                try:
                    num = int(var.get())
                    if not (0 <= num <= 9):
                        raise ValueError
                    numbers.append(num)
                except ValueError:
                    messagebox.showwarning("警告", "号码必须是0-9之间的数字")
                    return
            
            try:
                # 读取现有Excel文件
                df = pd.read_excel(data_file)
                
                # 确保期号列为字符串类型
                df['期号'] = df['期号'].astype(str)
                current_period = str(period_var.get()).strip()
                
                if edit_period:
                    # 修改模式：更新现有行
                    mask = df['期号'] == current_period
                    df.loc[mask, ['千位', '百位', '十位', '个位', '球五']] = numbers
                else:
                    # 添加模式：检查期号是否已存在
                    if current_period in df['期号'].values:
                        messagebox.showerror("错误", "该期号已存在，每个期号必须是唯一的")
                        return
                    
                    # 创建新行数据
                    new_data = {
                        '期号': current_period,
                        '千位': numbers[0],
                        '百位': numbers[1],
                        '十位': numbers[2],
                        '个位': numbers[3],
                        '球五': numbers[4]
                    }
                    
                    # 直接使用loc添加新行到最后
                    df.loc[len(df)] = new_data
                
                # 保存到Excel，保持原有顺序
                df.to_excel(data_file, index=False)
                
                messagebox.showinfo("成功", "开奖号码已保存")
                dialog.destroy()
                
                # 重新加载数据
                self.file_path_var.set(data_file)
                self.auto_load_last_file()
                
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")
        
        btn_frame = tk.Frame(input_frame)
        btn_frame.pack(fill=tk.X, pady=20)
        
        save_btn = tk.Button(btn_frame, text="保存", command=save_result,
                           bg="#4CAF50", fg="white", font=("SimHei", 10),
                           relief=tk.RAISED, padx=20)
        save_btn.pack(side=tk.LEFT, padx=10)
        
        cancel_btn = tk.Button(btn_frame, text="取消", command=dialog.destroy,
                             bg="#f44336", fg="white", font=("SimHei", 10),
                             relief=tk.RAISED, padx=20)
        cancel_btn.pack(side=tk.LEFT, padx=10)
        
        # 设置初始焦点
        if edit_period:
            number_entries[0].focus()
        else:
            if not period_var.get():
                period_entry.focus()
            else:
                number_entries[0].focus()
    def show_edit_result_dialog(self):
        """显示修改开奖号码的对话框"""
        data_file = "F:/pl5/data.xlsx"
        try:
            df = pd.read_excel(data_file)
            df['期号'] = df['期号'].astype(str)
        except Exception as e:
            messagebox.showerror("错误", f"无法读取F:/pl5/data.xlsx文件: {str(e)}")
            return
        
        # 创建期号选择对话框
        select_dialog = tk.Toplevel(self.root)
        select_dialog.title("选择要修改的期号")
        select_dialog.geometry("300x400")
        select_dialog.resizable(False, False)
        select_dialog.transient(self.root)
        select_dialog.grab_set()
        
        # 创建列表框架
        frame = tk.Frame(select_dialog, padx=20, pady=10)
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建滚动条
        scrollbar = tk.Scrollbar(frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 创建列表框
        listbox = tk.Listbox(frame, yscrollcommand=scrollbar.set, font=("SimHei", 10))
        listbox.pack(fill=tk.BOTH, expand=True)
        
        scrollbar.config(command=listbox.yview)
        
        # 添加期号到列表
        periods = df['期号'].tolist()
        for period in reversed(periods):  # 倒序显示，最新的在最上面
            listbox.insert(tk.END, period)
        
        def on_select():
            selection = listbox.curselection()
            if not selection:
                messagebox.showwarning("警告", "请选择要修改的期号")
                return
            period = listbox.get(selection[0])
            select_dialog.destroy()
            self.show_add_result_dialog(edit_period=period)
        
        # 按钮框架
        btn_frame = tk.Frame(select_dialog)
        btn_frame.pack(fill=tk.X, pady=10)
        
        select_btn = tk.Button(btn_frame, text="修改", command=on_select,
                             bg="#4CAF50", fg="white", font=("SimHei", 10),
                             relief=tk.RAISED, padx=20)
        select_btn.pack(side=tk.LEFT, padx=10)
        
        cancel_btn = tk.Button(btn_frame, text="取消", command=select_dialog.destroy,
                             bg="#f44336", fg="white", font=("SimHei", 10),
                             relief=tk.RAISED, padx=20)
        cancel_btn.pack(side=tk.LEFT, padx=10)
        
        # 双击选择
        listbox.bind('<Double-Button-1>', lambda e: on_select())
        
        # 设置初始焦点
        listbox.focus_set()
        if listbox.size() > 0:
            listbox.select_set(0)

    def init_params(self):
        """初始化参数"""
        if not os.path.exists(INIT_FILE):
            messagebox.showerror("错误", "未找到初始化配置文件(init.json)")
            return
            
        # 显示确认对话框
        if not messagebox.askyesno("确认", "确定要使用初始化参数替换当前参数吗？\n此操作将覆盖现有的参数设置。"):
            return
            
        success, message = apply_init_config()
        if success:
            # 重新加载配置
            self.config = load_config()
            self.default_params = {k: v for k, v in self.config.items() if k != 'last_excel_file'}
            self.model = MFTNModel(self.default_params)
            
            messagebox.showinfo("成功", message)
            
            # 如果已经加载了数据，询问是否要重新运行预测
            if self.history_data is not None:
                if messagebox.askyesno("提示", "参数已更新，是否要重新运行预测？"):
                    self.run_backtest_and_predict()
        else:
            messagebox.showerror("错误", message)
    
    def test_prediction_consistency(self):
        """测试预测一致性"""
        if self.history_data is None:
            messagebox.showwarning("警告", "请先加载数据")
            return
        
        # 创建测试对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("预测一致性测试")
        dialog.geometry("800x600")
        dialog.resizable(True, True)
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 创建主框架
        main_frame = tk.Frame(dialog, padx=20, pady=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = tk.Label(main_frame, text="预测一致性测试", font=("SimHei", 12, "bold"))
        title_label.pack(pady=10)
        
        # 创建文本框和滚动条
        text_frame = tk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        scrollbar = tk.Scrollbar(text_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        text_widget = tk.Text(text_frame, yscrollcommand=scrollbar.set, 
                             font=("SimHei", 9), wrap=tk.WORD)
        text_widget.pack(fill=tk.BOTH, expand=True)
        
        scrollbar.config(command=text_widget.yview)
        
        # 执行测试
        text_widget.insert(tk.END, "开始预测一致性测试...\n\n")
        
        # 测试2025200期的预测
        test_period = "2025200"
        
        # 找到该期在数据中的位置
        try:
            period_index = list(self.periods_data).index(test_period)
        except ValueError:
            text_widget.insert(tk.END, f"错误: 找不到期号 {test_period} 在数据中的位置\n")
            return
        
        text_widget.insert(tk.END, f"测试期号: {test_period}\n")
        text_widget.insert(tk.END, f"在数据中的位置: {period_index}\n")
        text_widget.insert(tk.END, f"训练数据期数: {period_index}\n\n")
        
        # 使用该期之前的数据进行预测
        train_data = self.history_data[:period_index]
        current_model = MFTNModel(self.get_current_params())
        current_model.fit(train_data)
        predictions = current_model.predict_next(is_predict=True)
        
        text_widget.insert(tk.END, "当前预测结果:\n")
        position_names = ['千位', '百位', '十位', '个位', '球五']
        for pos in range(5):
            nums = sorted(predictions[pos])
            nums_text = "".join(map(str, nums))
            text_widget.insert(tk.END, f"  {position_names[pos]}: {nums_text}\n")
        
        text_widget.insert(tk.END, f"\n当前参数:\n")
        params = self.get_current_params()
        for key, value in params.items():
            if key in ['lambda', 'co_weight', 'digit_transition_weight', 'sum_transition_weight', 'digit_exclusion_weight']:
                text_widget.insert(tk.END, f"  {key}: {value:.6f}\n")
        
        # 显示训练数据的最后几期
        text_widget.insert(tk.END, f"\n训练数据的最后5期:\n")
        for i in range(max(0, period_index-5), period_index):
            period = self.periods_data[i]
            numbers = self.history_data[i]
            text_widget.insert(tk.END, f"  {period}: {numbers}\n")
        
        text_widget.insert(tk.END, f"\n说明: 这是使用当前参数和{period_index}期历史数据对{test_period}期的预测结果。\n")
        text_widget.insert(tk.END, f"如果这个结果与您昨天的预测不一致，说明参数或算法发生了变化。\n")
        text_widget.insert(tk.END, f"请对比这个结果与您昨天的预测结果。\n")
        
        text_widget.config(state=tk.DISABLED)  # 设置为只读
    
    def detailed_debug(self):
        """详细调试功能"""
        if self.history_data is None:
            messagebox.showwarning("警告", "请先加载数据")
            return
        
        # 创建调试对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("详细调试")
        dialog.geometry("1000x700")
        dialog.resizable(True, True)
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 创建主框架
        main_frame = tk.Frame(dialog, padx=20, pady=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = tk.Label(main_frame, text="详细调试信息", font=("SimHei", 12, "bold"))
        title_label.pack(pady=10)
        
        # 创建文本框和滚动条
        text_frame = tk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        scrollbar = tk.Scrollbar(text_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        text_widget = tk.Text(text_frame, yscrollcommand=scrollbar.set, 
                             font=("SimHei", 9), wrap=tk.WORD)
        text_widget.pack(fill=tk.BOTH, expand=True)
        
        scrollbar.config(command=text_widget.yview)
        
        # 执行详细调试
        text_widget.insert(tk.END, "=== 详细调试信息 ===\n\n")
        
        # 1. 显示数据基本信息
        text_widget.insert(tk.END, "1. 数据基本信息:\n")
        text_widget.insert(tk.END, f"   总数据量: {len(self.history_data)}\n")
        text_widget.insert(tk.END, f"   最后一期: {self.last_period}\n")
        text_widget.insert(tk.END, f"   数据文件: {self.file_path_var.get()}\n")
        
        # 检查回撤天数设置
        try:
            current_drawdown = int(self.drawdown_days_var.get())
            text_widget.insert(tk.END, f"   当前回撤天数设置: {current_drawdown}\n")
            if current_drawdown > 0:
                text_widget.insert(tk.END, f"   ⚠️ 警告: 回撤天数设置为{current_drawdown}，这会影响预测结果！\n")
                text_widget.insert(tk.END, f"   实际可用数据量: {len(self.history_data) - current_drawdown}\n")
        except:
            text_widget.insert(tk.END, f"   回撤天数设置: 无法读取\n")
        text_widget.insert(tk.END, "\n")
        
        # 2. 显示参数信息
        text_widget.insert(tk.END, "2. 当前参数:\n")
        params = self.get_current_params()
        for key, value in params.items():
            text_widget.insert(tk.END, f"   {key}: {value}\n")
        text_widget.insert(tk.END, "\n")
        
        # 3. 显示2025200期的详细信息
        test_period = "2025200"
        try:
            # 调试期号数据类型
            text_widget.insert(tk.END, f"期号数据类型检查:\n")
            text_widget.insert(tk.END, f"   期号数据类型: {type(self.periods_data[0])}\n")
            text_widget.insert(tk.END, f"   前5个期号: {self.periods_data[:5]}\n")
            text_widget.insert(tk.END, f"   后5个期号: {self.periods_data[-5:]}\n")
            text_widget.insert(tk.END, f"   查找的期号类型: {type(test_period)}\n")
            text_widget.insert(tk.END, f"   查找的期号值: '{test_period}'\n\n")
            
            # 尝试不同的匹配方式
            period_index = None
            for i, period in enumerate(self.periods_data):
                if str(period) == test_period:
                    period_index = i
                    break
            
            if period_index is None:
                text_widget.insert(tk.END, f"错误: 找不到期号 {test_period}\n")
                text_widget.insert(tk.END, f"请检查期号格式是否正确\n")
                return
            text_widget.insert(tk.END, f"3. {test_period}期详细信息:\n")
            text_widget.insert(tk.END, f"   在数据中的位置: {period_index}\n")
            text_widget.insert(tk.END, f"   实际开奖号码: {self.history_data[period_index]}\n")
            text_widget.insert(tk.END, f"   训练数据期数: {period_index}\n\n")
            
            # 4. 显示训练数据的详细信息
            text_widget.insert(tk.END, "4. 训练数据详细信息:\n")
            
            # 考虑回撤天数的影响
            try:
                current_drawdown = int(self.drawdown_days_var.get())
                if current_drawdown > 0:
                    text_widget.insert(tk.END, f"   ⚠️ 注意: 回撤天数设置为{current_drawdown}\n")
                    text_widget.insert(tk.END, f"   实际训练数据会排除最后{current_drawdown}期\n")
                    # 计算实际可用的训练数据
                    actual_train_end = period_index - current_drawdown
                    if actual_train_end <= 0:
                        text_widget.insert(tk.END, f"   ❌ 错误: 回撤天数过大，没有足够的训练数据\n")
                        return
                    train_data = self.history_data[:actual_train_end]
                    text_widget.insert(tk.END, f"   实际训练数据期数: {actual_train_end}\n")
                else:
                    train_data = self.history_data[:period_index]
                    text_widget.insert(tk.END, f"   训练数据期数: {period_index}\n")
            except:
                train_data = self.history_data[:period_index]
                text_widget.insert(tk.END, f"   训练数据期数: {period_index}\n")
            
            text_widget.insert(tk.END, f"   训练数据形状: {train_data.shape}\n")
            text_widget.insert(tk.END, f"   训练数据最后10期:\n")
            for i in range(max(0, len(train_data)-10), len(train_data)):
                period = self.periods_data[i]
                numbers = self.history_data[i]
                text_widget.insert(tk.END, f"     {period}: {numbers}\n")
            text_widget.insert(tk.END, "\n")
            
            # 5. 执行预测并显示详细信息
            text_widget.insert(tk.END, "5. 预测过程:\n")
            current_model = MFTNModel(self.get_current_params())
            current_model.fit(train_data)
            
            # 显示模型内部状态
            text_widget.insert(tk.END, f"   模型历史数据形状: {current_model.history.shape}\n")
            text_widget.insert(tk.END, f"   热力状态形状: {current_model.heat_index.shape}\n")
            text_widget.insert(tk.END, f"   遗漏计数形状: {current_model.miss_counts.shape}\n")
            text_widget.insert(tk.END, f"   相关系数矩阵形状: {current_model.corr_matrix.shape}\n\n")
            
            # 执行预测
            text_widget.insert(tk.END, f"   预测模式: 正式预测模式 (is_predict=True)\n")
            predictions = current_model.predict_next(is_predict=True)
            text_widget.insert(tk.END, "6. 预测结果:\n")
            position_names = ['千位', '百位', '十位', '个位', '球五']
            for pos in range(5):
                nums = sorted(predictions[pos])
                nums_text = "".join(map(str, nums))
                text_widget.insert(tk.END, f"   {position_names[pos]}: {nums_text}\n")
            text_widget.insert(tk.END, "\n")
            
            # 7. 显示命中情况
            text_widget.insert(tk.END, "7. 命中情况:\n")
            actual_numbers = self.history_data[period_index]
            for pos in range(5):
                predicted_nums = predictions[pos]
                actual_num = actual_numbers[pos]
                hit = actual_num in predicted_nums
                hit_text = "✓" if hit else "✗"
                text_widget.insert(tk.END, f"   {position_names[pos]}: 预测{predicted_nums}, 实际{actual_num} {hit_text}\n")
            
        except ValueError:
            text_widget.insert(tk.END, f"错误: 找不到期号 {test_period} 在数据中的位置\n")
        
        text_widget.insert(tk.END, "\n=== 调试完成 ===\n")
        text_widget.insert(tk.END, "请对比这些信息与您昨天的预测结果。\n")
        text_widget.insert(tk.END, "如果预测结果不一致，请检查:\n")
        text_widget.insert(tk.END, "1. 参数是否发生变化\n")
        text_widget.insert(tk.END, "2. 训练数据是否发生变化\n")
        text_widget.insert(tk.END, "3. 算法逻辑是否发生变化\n")
        text_widget.insert(tk.END, "4. 模型内部随机性\n\n")
        
        # 添加多次预测测试
        text_widget.insert(tk.END, "=== 多次预测测试 ===\n")
        text_widget.insert(tk.END, "执行5次相同预测，检查是否有随机性:\n")
        
        for test_num in range(5):
            test_model = MFTNModel(self.get_current_params())
            test_model.fit(train_data)
            test_predictions = test_model.predict_next(is_predict=True)
            
            text_widget.insert(tk.END, f"\n第{test_num+1}次预测:\n")
            for pos in range(5):
                nums = sorted(test_predictions[pos])
                nums_text = "".join(map(str, nums))
                text_widget.insert(tk.END, f"   {position_names[pos]}: {nums_text}\n")
        
        text_widget.insert(tk.END, "\n如果多次预测结果相同，说明没有随机性问题。\n")
        text_widget.insert(tk.END, "如果结果不同，说明模型有随机性，这可能是导致不一致的原因。\n\n")
        
        # 添加正式预测模式测试
        text_widget.insert(tk.END, "=== 正式预测模式测试 ===\n")
        text_widget.insert(tk.END, "使用正式预测模式 (is_predict=True):\n")
        
        formal_model = MFTNModel(self.get_current_params())
        formal_model.fit(train_data)
        formal_predictions = formal_model.predict_next(is_predict=True)
        
        text_widget.insert(tk.END, "正式预测结果:\n")
        for pos in range(5):
            nums = sorted(formal_predictions[pos])
            nums_text = "".join(map(str, nums))
            text_widget.insert(tk.END, f"   {position_names[pos]}: {nums_text}\n")
        
        text_widget.insert(tk.END, "\n对比回测模式和正式预测模式的结果。\n")
        text_widget.insert(tk.END, "如果结果不同，说明预测模式是导致不一致的原因。\n")
        
        text_widget.config(state=tk.DISABLED)  # 设置为只读

def main():
    root = tk.Tk()
    root.iconbitmap(default="")  # 避免显示默认图标
    app = LotteryPredictionApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()